import { NextRequest, NextResponse } from 'next/server'
import { appointmentsService } from '@/lib/services/appointments-service'

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    const customerId = url.searchParams.get('customerId')
    const installerId = url.searchParams.get('installerId')
    const id = url.searchParams.get('id')

    // Get single appointment by ID
    if (id) {
      const appointment = await appointmentsService.getAppointmentById(id)
      if (!appointment) {
        return NextResponse.json({
          status: 'error',
          message: 'Appointment not found'
        }, { status: 404 })
      }
      return NextResponse.json({
        status: 'success',
        data: appointment
      })
    }

    // Get appointments by customer
    if (customerId) {
      const appointments = await appointmentsService.getAppointmentsByCustomer(customerId)
      return NextResponse.json({
        status: 'success',
        data: appointments
      })
    }

    // Get appointments by installer
    if (installerId) {
      const appointments = await appointmentsService.getAppointmentsByInstaller(installerId)
      return NextResponse.json({
        status: 'success',
        data: appointments
      })
    }

    // Get all appointments with optional date range
    const appointments = await appointmentsService.getAppointments(
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined
    )
    
    return NextResponse.json({
      status: 'success',
      data: appointments
    })
    
  } catch (error) {
    console.error('Appointments API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch appointments',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Basic validation
    if (!body.projectId || !body.customerId || !body.appointmentDate || 
        !body.startTime || !body.endTime || !body.installerId || 
        !body.location || !body.createdBy) {
      return NextResponse.json({
        status: 'error',
        message: 'Missing required fields: projectId, customerId, appointmentDate, startTime, endTime, installerId, location, createdBy'
      }, { status: 400 })
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(body.appointmentDate)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid appointment date format. Use YYYY-MM-DD'
      }, { status: 400 })
    }

    // Validate time format
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/
    if (!timeRegex.test(body.startTime) || !timeRegex.test(body.endTime)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid time format. Use HH:MM:SS'
      }, { status: 400 })
    }

    // Validate status
    const validStatuses = ['scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'rescheduled']
    if (body.status && !validStatuses.includes(body.status)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      }, { status: 400 })
    }

    // Set defaults
    const appointmentData = {
      ...body,
      duration: body.duration || 180,
      estimatedDuration: body.estimatedDuration || 180,
      productTypes: body.productTypes || [],
      sendNotification: body.sendNotification ?? true,
      autoReminder: body.autoReminder ?? true,
      smsReminder: body.smsReminder ?? false,
      emailReminder: body.emailReminder ?? true,
      status: body.status || 'scheduled'
    }

    const appointment = await appointmentsService.createAppointment(appointmentData)
    
    if (!appointment) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to create appointment'
      }, { status: 400 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: appointment
    }, { status: 201 })
    
  } catch (error) {
    console.error('Create appointment API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to create appointment',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'Appointment ID is required'
      }, { status: 400 })
    }

    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid appointment ID format'
      }, { status: 400 })
    }

    // Validate date format if provided
    if (updateData.appointmentDate && !/^\d{4}-\d{2}-\d{2}$/.test(updateData.appointmentDate)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid appointment date format. Use YYYY-MM-DD'
      }, { status: 400 })
    }

    // Validate time format if provided
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/
    if (updateData.startTime && !timeRegex.test(updateData.startTime)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid start time format. Use HH:MM:SS'
      }, { status: 400 })
    }
    if (updateData.endTime && !timeRegex.test(updateData.endTime)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid end time format. Use HH:MM:SS'
      }, { status: 400 })
    }

    // Validate status if provided
    const validStatuses = ['scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'rescheduled']
    if (updateData.status && !validStatuses.includes(updateData.status)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      }, { status: 400 })
    }

    const appointment = await appointmentsService.updateAppointment(id, updateData)
    
    if (!appointment) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to update appointment or appointment not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: appointment
    })
    
  } catch (error) {
    console.error('Update appointment API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to update appointment',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'Appointment ID is required'
      }, { status: 400 })
    }
    
    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid appointment ID format'
      }, { status: 400 })
    }
    
    const success = await appointmentsService.deleteAppointment(id)
    
    if (!success) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to delete appointment'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      message: 'Appointment deleted successfully'
    })
    
  } catch (error) {
    console.error('Delete appointment API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to delete appointment',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 