import { supabase } from '@/lib/supabase/client'
import type { DashboardStats, RecentActivity } from '@/types'

export class DashboardService {
  private supabase = supabase

  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get data from the dashboard_stats view
      const { data: statsData, error: statsError } = await this.supabase
        .from('dashboard_stats')
        .select('*')
        .single()

      if (statsError) {
        console.error('Failed to fetch dashboard stats:', statsError)
        // Return default values if the view doesn't exist or has no data
        return {
          totalProjects: 0,
          activeProjects: 0,
          completedProjects: 0,
          totalCustomers: 0,
          revenueThisMonth: 0,
          revenueLastMonth: 0,
          averageProjectValue: 0,
          upcomingTasks: 0,
        }
      }

      return {
        totalProjects: statsData.total_projects ?? 0,
        activeProjects: statsData.active_projects ?? 0,
        completedProjects: statsData.completed_projects ?? 0,
        totalCustomers: statsData.total_customers ?? 0,
        revenueThisMonth: statsData.revenue_this_month ?? 0,
        revenueLastMonth: statsData.revenue_last_month ?? 0,
        averageProjectValue: statsData.average_project_value ?? 0,
        upcomingTasks: statsData.upcoming_tasks ?? 0,
      }
    } catch (error) {
      console.error('Dashboard stats service error:', error)
      // Return default values on error
      return {
        totalProjects: 0,
        activeProjects: 0,
        completedProjects: 0,
        totalCustomers: 0,
        revenueThisMonth: 0,
        revenueLastMonth: 0,
        averageProjectValue: 0,
        upcomingTasks: 0,
      }
    }
  }

  async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    try {
      const { data, error } = await this.supabase
        .from('activity_log')
        .select(`
          id,
          type,
          title,
          description,
          created_at,
          entity_id,
          user_id,
          users (
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('Failed to fetch recent activity:', error)
        return []
      }

      return (data || []).map((activity) => ({
        id: activity.id,
        type: activity.type as RecentActivity['type'],
        title: activity.title,
        description: activity.description,
        timestamp: this.formatTimestamp(activity.created_at),
        user: activity.users 
          ? `${activity.users.first_name} ${activity.users.last_name}`
          : 'Unknown User',
        entityId: activity.entity_id || undefined,
      }))
    } catch (error) {
      console.error('Recent activity service error:', error)
      return []
    }
  }

  async getTodaysTasks() {
    try {
      const today = new Date()
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

      const { data, error } = await this.supabase
        .from('installation_tasks')
        .select(`
          id,
          title,
          status,
          start_time,
          end_time,
          location_street,
          location_city,
          location_state,
          project_id,
          projects (
            customers (
              first_name,
              last_name
            )
          )
        `)
        .gte('start_time', todayStart.toISOString())
        .lt('start_time', todayEnd.toISOString())
        .order('start_time', { ascending: true })

      if (error) {
        console.error('Failed to fetch today\'s tasks:', error)
        return []
      }

      return (data || []).map((task) => ({
        id: task.id,
        title: task.title,
        customer: task.projects?.customers 
          ? `${task.projects.customers.first_name} ${task.projects.customers.last_name}`
          : 'Unknown Customer',
        time: task.start_time ? this.formatTime(task.start_time) : 'No time set',
        location: this.formatLocationFields(task.location_street, task.location_city, task.location_state),
        status: task.status,
      }))
    } catch (error) {
      console.error('Today\'s tasks service error:', error)
      return []
    }
  }

  private formatTimestamp(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`
    } else if (diffInMinutes < 1440) { // 24 hours
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours} hour${hours > 1 ? 's' : ''} ago`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days} day${days > 1 ? 's' : ''} ago`
    }
  }

  private formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  private formatLocationFields(street: string, city: string, state: string): string {
    const parts = [street, city, state].filter(Boolean)
    return parts.length > 0 ? parts.join(', ') : 'Unknown Location'
  }

  private formatLocation(location: Record<string, unknown>): string {
    if (typeof location === 'string') {
      return location
    }
    if (location && typeof location === 'object') {
      const { street, city, state } = location
      return `${street || ''}, ${city || ''}, ${state || ''}`.replace(/^,\s*|,\s*$/g, '')
    }
    return 'Unknown Location'
  }
}

export const dashboardService = new DashboardService()
