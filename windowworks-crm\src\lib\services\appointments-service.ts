import { supabase } from '@/lib/supabase/client'

export interface Appointment {
  id: string
  projectId: string
  customerId: string
  appointmentDate: string
  startTime: string
  endTime: string
  duration: number
  estimatedDuration: number
  installerId: string
  location: string
  productTypes: string[]
  notes?: string
  sendNotification: boolean
  autoReminder: boolean
  smsReminder: boolean
  emailReminder: boolean
  status: AppointmentStatus
  createdAt: string
  updatedAt: string
  createdBy: string
  // Related data
  project?: {
    id: string
    title: string
    description: string
  }
  customer?: {
    id: string
    firstName: string
    lastName: string
    email: string
    phone?: string
  }
  installer?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
}

export type AppointmentStatus = 'scheduled' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled' | 'rescheduled'

export class AppointmentsService {
  private supabase = supabase

  async getAppointments(startDate?: Date, endDate?: Date): Promise<Appointment[]> {
    try {
      console.log('Fetching appointments from database...')
      
      let query = this.supabase
        .from('appointments')
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .order('appointment_date', { ascending: true })
        .order('start_time', { ascending: true })

      if (startDate) {
        query = query.gte('appointment_date', startDate.toISOString().split('T')[0])
      }
      if (endDate) {
        query = query.lte('appointment_date', endDate.toISOString().split('T')[0])
      }

      const { data, error } = await query

      console.log('Appointments query response:', { data, error })

      if (error) {
        console.error('Failed to fetch appointments:', error)
        return []
      }

      const mappedAppointments = (data || []).map((appointment) => ({
        id: appointment.id,
        projectId: appointment.project_id,
        customerId: appointment.customer_id,
        appointmentDate: appointment.appointment_date,
        startTime: appointment.start_time,
        endTime: appointment.end_time,
        duration: appointment.duration,
        estimatedDuration: appointment.estimated_duration,
        installerId: appointment.installer_id,
        location: appointment.location,
        productTypes: (appointment.product_types as string[]) || [],
        notes: appointment.notes || undefined,
        sendNotification: appointment.send_notification ?? true,
        autoReminder: appointment.auto_reminder ?? true,
        smsReminder: appointment.sms_reminder ?? false,
        emailReminder: appointment.email_reminder ?? true,
        status: appointment.status as AppointmentStatus,
        createdAt: appointment.created_at,
        updatedAt: appointment.updated_at,
        createdBy: appointment.created_by,
        project: appointment.projects ? {
          id: appointment.projects.id,
          title: appointment.projects.title,
          description: appointment.projects.description
        } : undefined,
        customer: appointment.customers ? {
          id: appointment.customers.id,
          firstName: appointment.customers.first_name,
          lastName: appointment.customers.last_name,
          email: appointment.customers.email,
          phone: appointment.customers.phone || undefined
        } : undefined,
        installer: appointment.users ? {
          id: appointment.users.id,
          firstName: appointment.users.first_name,
          lastName: appointment.users.last_name,
          email: appointment.users.email
        } : undefined
      }))

      console.log('Mapped appointments:', mappedAppointments)
      return mappedAppointments
    } catch (error) {
      console.error('Appointments service error:', error)
      return []
    }
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    try {
      const { data, error } = await this.supabase
        .from('appointments')
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Failed to fetch appointment:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        customerId: data.customer_id,
        appointmentDate: data.appointment_date,
        startTime: data.start_time,
        endTime: data.end_time,
        duration: data.duration,
        estimatedDuration: data.estimated_duration,
        installerId: data.installer_id,
        location: data.location,
        productTypes: (data.product_types as string[]) || [],
        notes: data.notes || undefined,
        sendNotification: data.send_notification ?? true,
        autoReminder: data.auto_reminder ?? true,
        smsReminder: data.sms_reminder ?? false,
        emailReminder: data.email_reminder ?? true,
        status: data.status as AppointmentStatus,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: data.created_by,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        customer: data.customers ? {
          id: data.customers.id,
          firstName: data.customers.first_name,
          lastName: data.customers.last_name,
          email: data.customers.email,
          phone: data.customers.phone || undefined
        } : undefined,
        installer: data.users ? {
          id: data.users.id,
          firstName: data.users.first_name,
          lastName: data.users.last_name,
          email: data.users.email
        } : undefined
      }
    } catch (error) {
      console.error('Get appointment by ID service error:', error)
      return null
    }
  }

  async getAppointmentsByCustomer(customerId: string): Promise<Appointment[]> {
    try {
      const { data, error } = await this.supabase
        .from('appointments')
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('customer_id', customerId)
        .order('appointment_date', { ascending: true })

      if (error) {
        console.error('Failed to fetch appointments by customer:', error)
        return []
      }

      return (data || []).map((appointment) => ({
        id: appointment.id,
        projectId: appointment.project_id,
        customerId: appointment.customer_id,
        appointmentDate: appointment.appointment_date,
        startTime: appointment.start_time,
        endTime: appointment.end_time,
        duration: appointment.duration,
        estimatedDuration: appointment.estimated_duration,
        installerId: appointment.installer_id,
        location: appointment.location,
        productTypes: (appointment.product_types as string[]) || [],
        notes: appointment.notes || undefined,
        sendNotification: appointment.send_notification ?? true,
        autoReminder: appointment.auto_reminder ?? true,
        smsReminder: appointment.sms_reminder ?? false,
        emailReminder: appointment.email_reminder ?? true,
        status: appointment.status as AppointmentStatus,
        createdAt: appointment.created_at,
        updatedAt: appointment.updated_at,
        createdBy: appointment.created_by,
        project: appointment.projects ? {
          id: appointment.projects.id,
          title: appointment.projects.title,
          description: appointment.projects.description
        } : undefined,
        customer: appointment.customers ? {
          id: appointment.customers.id,
          firstName: appointment.customers.first_name,
          lastName: appointment.customers.last_name,
          email: appointment.customers.email,
          phone: appointment.customers.phone || undefined
        } : undefined,
        installer: appointment.users ? {
          id: appointment.users.id,
          firstName: appointment.users.first_name,
          lastName: appointment.users.last_name,
          email: appointment.users.email
        } : undefined
      }))
    } catch (error) {
      console.error('Get appointments by customer service error:', error)
      return []
    }
  }

  async getAppointmentsByInstaller(installerId: string): Promise<Appointment[]> {
    try {
      const { data, error } = await this.supabase
        .from('appointments')
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .eq('installer_id', installerId)
        .order('appointment_date', { ascending: true })

      if (error) {
        console.error('Failed to fetch appointments by installer:', error)
        return []
      }

      return (data || []).map((appointment) => ({
        id: appointment.id,
        projectId: appointment.project_id,
        customerId: appointment.customer_id,
        appointmentDate: appointment.appointment_date,
        startTime: appointment.start_time,
        endTime: appointment.end_time,
        duration: appointment.duration,
        estimatedDuration: appointment.estimated_duration,
        installerId: appointment.installer_id,
        location: appointment.location,
        productTypes: (appointment.product_types as string[]) || [],
        notes: appointment.notes || undefined,
        sendNotification: appointment.send_notification ?? true,
        autoReminder: appointment.auto_reminder ?? true,
        smsReminder: appointment.sms_reminder ?? false,
        emailReminder: appointment.email_reminder ?? true,
        status: appointment.status as AppointmentStatus,
        createdAt: appointment.created_at,
        updatedAt: appointment.updated_at,
        createdBy: appointment.created_by,
        project: appointment.projects ? {
          id: appointment.projects.id,
          title: appointment.projects.title,
          description: appointment.projects.description
        } : undefined,
        customer: appointment.customers ? {
          id: appointment.customers.id,
          firstName: appointment.customers.first_name,
          lastName: appointment.customers.last_name,
          email: appointment.customers.email,
          phone: appointment.customers.phone || undefined
        } : undefined,
        installer: appointment.users ? {
          id: appointment.users.id,
          firstName: appointment.users.first_name,
          lastName: appointment.users.last_name,
          email: appointment.users.email
        } : undefined
      }))
    } catch (error) {
      console.error('Get appointments by installer service error:', error)
      return []
    }
  }

  async createAppointment(appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>): Promise<Appointment | null> {
    try {
      const { data, error } = await this.supabase
        .from('appointments')
        .insert({
          project_id: appointmentData.projectId,
          customer_id: appointmentData.customerId,
          appointment_date: appointmentData.appointmentDate,
          start_time: appointmentData.startTime,
          end_time: appointmentData.endTime,
          duration: appointmentData.duration,
          estimated_duration: appointmentData.estimatedDuration,
          installer_id: appointmentData.installerId,
          location: appointmentData.location,
          product_types: appointmentData.productTypes,
          notes: appointmentData.notes,
          send_notification: appointmentData.sendNotification,
          auto_reminder: appointmentData.autoReminder,
          sms_reminder: appointmentData.smsReminder,
          email_reminder: appointmentData.emailReminder,
          status: appointmentData.status,
          created_by: appointmentData.createdBy
        })
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single()

      if (error) {
        console.error('Failed to create appointment:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        customerId: data.customer_id,
        appointmentDate: data.appointment_date,
        startTime: data.start_time,
        endTime: data.end_time,
        duration: data.duration,
        estimatedDuration: data.estimated_duration,
        installerId: data.installer_id,
        location: data.location,
        productTypes: (data.product_types as string[]) || [],
        notes: data.notes || undefined,
        sendNotification: data.send_notification ?? true,
        autoReminder: data.auto_reminder ?? true,
        smsReminder: data.sms_reminder ?? false,
        emailReminder: data.email_reminder ?? true,
        status: data.status as AppointmentStatus,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: data.created_by,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        customer: data.customers ? {
          id: data.customers.id,
          firstName: data.customers.first_name,
          lastName: data.customers.last_name,
          email: data.customers.email,
          phone: data.customers.phone || undefined
        } : undefined,
        installer: data.users ? {
          id: data.users.id,
          firstName: data.users.first_name,
          lastName: data.users.last_name,
          email: data.users.email
        } : undefined
      }
    } catch (error) {
      console.error('Create appointment service error:', error)
      return null
    }
  }

  async updateAppointment(id: string, appointmentData: Partial<Appointment>): Promise<Appointment | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (appointmentData.projectId !== undefined) updateData.project_id = appointmentData.projectId
      if (appointmentData.customerId !== undefined) updateData.customer_id = appointmentData.customerId
      if (appointmentData.appointmentDate !== undefined) updateData.appointment_date = appointmentData.appointmentDate
      if (appointmentData.startTime !== undefined) updateData.start_time = appointmentData.startTime
      if (appointmentData.endTime !== undefined) updateData.end_time = appointmentData.endTime
      if (appointmentData.duration !== undefined) updateData.duration = appointmentData.duration
      if (appointmentData.estimatedDuration !== undefined) updateData.estimated_duration = appointmentData.estimatedDuration
      if (appointmentData.installerId !== undefined) updateData.installer_id = appointmentData.installerId
      if (appointmentData.location !== undefined) updateData.location = appointmentData.location
      if (appointmentData.productTypes !== undefined) updateData.product_types = appointmentData.productTypes
      if (appointmentData.notes !== undefined) updateData.notes = appointmentData.notes
      if (appointmentData.sendNotification !== undefined) updateData.send_notification = appointmentData.sendNotification
      if (appointmentData.autoReminder !== undefined) updateData.auto_reminder = appointmentData.autoReminder
      if (appointmentData.smsReminder !== undefined) updateData.sms_reminder = appointmentData.smsReminder
      if (appointmentData.emailReminder !== undefined) updateData.email_reminder = appointmentData.emailReminder
      if (appointmentData.status !== undefined) updateData.status = appointmentData.status

      const { data, error } = await this.supabase
        .from('appointments')
        .update(updateData)
        .eq('id', id)
        .select(`
          id,
          project_id,
          customer_id,
          appointment_date,
          start_time,
          end_time,
          duration,
          estimated_duration,
          installer_id,
          location,
          product_types,
          notes,
          send_notification,
          auto_reminder,
          sms_reminder,
          email_reminder,
          status,
          created_at,
          updated_at,
          created_by,
          projects (
            id,
            title,
            description
          ),
          customers (
            id,
            first_name,
            last_name,
            email,
            phone
          ),
          users!installer_id (
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single()

      if (error) {
        console.error('Failed to update appointment:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        customerId: data.customer_id,
        appointmentDate: data.appointment_date,
        startTime: data.start_time,
        endTime: data.end_time,
        duration: data.duration,
        estimatedDuration: data.estimated_duration,
        installerId: data.installer_id,
        location: data.location,
        productTypes: (data.product_types as string[]) || [],
        notes: data.notes || undefined,
        sendNotification: data.send_notification ?? true,
        autoReminder: data.auto_reminder ?? true,
        smsReminder: data.sms_reminder ?? false,
        emailReminder: data.email_reminder ?? true,
        status: data.status as AppointmentStatus,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: data.created_by,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        customer: data.customers ? {
          id: data.customers.id,
          firstName: data.customers.first_name,
          lastName: data.customers.last_name,
          email: data.customers.email,
          phone: data.customers.phone || undefined
        } : undefined,
        installer: data.users ? {
          id: data.users.id,
          firstName: data.users.first_name,
          lastName: data.users.last_name,
          email: data.users.email
        } : undefined
      }
    } catch (error) {
      console.error('Update appointment service error:', error)
      return null
    }
  }

  async deleteAppointment(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('appointments')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete appointment:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete appointment service error:', error)
      return false
    }
  }

  async getAppointmentStats() {
    try {
      // Get total appointments count
      const { count: totalAppointments, error: totalError } = await this.supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })

      if (totalError) {
        console.error('Failed to get total appointments count:', totalError)
        return {
          totalAppointments: 0,
          appointmentsByStatus: {},
          upcomingAppointments: 0,
          todaysAppointments: 0
        }
      }

      // Get appointments by status
      const { data: statusData, error: statusError } = await this.supabase
        .from('appointments')
        .select('status')

      const appointmentsByStatus = statusData?.reduce((acc, appointment) => {
        acc[appointment.status] = (acc[appointment.status] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      // Get upcoming appointments (next 7 days)
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)
      
      const { count: upcomingAppointments, error: upcomingError } = await this.supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gte('appointment_date', new Date().toISOString().split('T')[0])
        .lte('appointment_date', nextWeek.toISOString().split('T')[0])

      // Get today's appointments
      const today = new Date().toISOString().split('T')[0]
      const { count: todaysAppointments, error: todayError } = await this.supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .eq('appointment_date', today)

      return {
        totalAppointments: totalAppointments || 0,
        appointmentsByStatus,
        upcomingAppointments: upcomingAppointments || 0,
        todaysAppointments: todaysAppointments || 0
      }
    } catch (error) {
      console.error('Get appointment stats service error:', error)
      return {
        totalAppointments: 0,
        appointmentsByStatus: {},
        upcomingAppointments: 0,
        todaysAppointments: 0
      }
    }
  }
}

export const appointmentsService = new AppointmentsService() 