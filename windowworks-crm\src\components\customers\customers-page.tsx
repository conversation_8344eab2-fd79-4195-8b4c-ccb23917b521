'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  ArrowUp, 
  Star, 
  DollarSign,
  Search,
  MoreHorizontal,
  Edit,
  ExternalLink,
  Trash2,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Plus,
  Download,
  Eye,
  CheckCircle,
  Clock,
  X,
  AlertCircle,
  RefreshCw
} from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { AccentButton } from '@/components/ui/accent-button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { NewCustomerModal } from '@/components/customers/new-customer-modal'
import { CustomerDetailsModal } from '@/components/customers/customer-details-modal'
import { EditCustomerModal } from '@/components/customers/edit-customer-modal'
import { CustomerProjectsModal } from '@/components/customers/customer-projects-modal'
import { useCustomersData } from '@/hooks/use-customers-data'
import { useDashboardData } from '@/hooks/use-dashboard-data'
import type { Customer, WindowTreatmentType, BudgetRange, CommunicationPreference } from '@/types'

// Types
interface CustomerFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  preferences: {
    windowTypes: string[]
    preferredColors: string[]
    budgetRange: string
    communicationMethod: string
  }
  notes: string
}

// Transform form data to match Customer type
const transformFormDataToCustomer = (formData: CustomerFormData): Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> => {
  return {
    firstName: formData.firstName,
    lastName: formData.lastName,
    email: formData.email,
    phone: formData.phone,
    address: formData.address,
    preferences: {
      windowTreatmentTypes: formData.preferences.windowTypes as WindowTreatmentType[],
      preferredColors: formData.preferences.preferredColors,
      budget: formData.preferences.budgetRange as BudgetRange,
      communication: formData.preferences.communicationMethod as CommunicationPreference
    },
    notes: formData.notes
  }
}

export default function CustomersPage() {
  const { customers, loading, error, refetch, createCustomer, deleteCustomer, getCustomerAppointmentCount, getCustomerProjectCount, updateCustomer } = useCustomersData()
  const { stats: dashboardStats } = useDashboardData()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [preferenceFilter, setPreferenceFilter] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showProjectsModal, setShowProjectsModal] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean
    customerId: string
    customerName: string
    appointmentCount?: number
    projectCount?: number
  }>({
    isOpen: false,
    customerId: '',
    customerName: '',
    appointmentCount: 0,
    projectCount: 0
  })

  // Show loading state
  if (loading && customers.length === 0) {
    return (
      <div className="space-y-6 p-1">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading customers...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error && customers.length === 0) {
    return (
      <div className="space-y-6 p-1">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">Failed to load customers</p>
            <Button onClick={refetch} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Convert Customer[] to the format expected by the UI
  const displayCustomers = customers.map(customer => ({
    id: customer.id,
    name: `${customer.firstName} ${customer.lastName}`,
    email: customer.email,
    phone: customer.phone || '',
    address: `${customer.address.street}, ${customer.address.city}, ${customer.address.state} ${customer.address.zipCode}`,
    status: 'Active', // You might want to add a status field to the Customer type
    lastProject: null, // This would need to be calculated from projects
    totalRevenue: 0, // This would need to be calculated from projects
    projectCount: 0, // This would need to be calculated from projects
    preferences: customer.preferences.windowTreatmentTypes,
    avatar: null
  }))

  // Filter and sort customers
  const filteredCustomers = displayCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || customer.status.toLowerCase() === statusFilter.toLowerCase()
    
    const matchesPreference = preferenceFilter === 'all' || 
                             customer.preferences.some(pref => pref.toLowerCase() === preferenceFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesPreference
  }).sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'date':
        return new Date(b.lastProject || 0).getTime() - new Date(a.lastProject || 0).getTime()
      case 'revenue':
        return b.totalRevenue - a.totalRevenue
      default:
        return 0
    }
  })

  const totalPages = Math.ceil(filteredCustomers.length / itemsPerPage)
  const paginatedCustomers = filteredCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleSelectCustomer = (customerId: string) => {
    setSelectedCustomers(prev => 
      prev.includes(customerId)
        ? prev.filter(id => id !== customerId)
        : [...prev, customerId]
    )
  }

  const handleSelectAll = () => {
    if (selectedCustomers.length === paginatedCustomers.length) {
      setSelectedCustomers([])
    } else {
      setSelectedCustomers(paginatedCustomers.map(customer => customer.id))
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'default'
      case 'pending':
        return 'secondary'
      case 'inactive':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return CheckCircle
      case 'pending':
        return Clock
      default:
        return X
    }
  }

  // Add customer save handler
  const handleSaveCustomer = async (customerData: CustomerFormData) => {
    try {
      console.log('Saving customer:', customerData)
      
      // Transform form data to match Customer type
      const customerToCreate = transformFormDataToCustomer(customerData)
      
      // Use the actual createCustomer function from the hook
      const newCustomer = await createCustomer(customerToCreate)
      
      if (newCustomer) {
        console.log('Customer saved successfully:', newCustomer)
        // Close the modal
        setShowNewCustomerModal(false)
      } else {
        throw new Error('Failed to create customer')
      }
    } catch (error) {
      console.error('Error saving customer:', error)
      // You might want to show a toast notification here
      alert('Failed to save customer. Please try again.')
      throw error
    }
  }

  // Handle customer deletion
  const handleDeleteCustomer = async (customerId: string, customerName: string) => {
    try {
      const appointmentCount = await getCustomerAppointmentCount(customerId)
      const projectCount = await getCustomerProjectCount(customerId)
      setDeleteConfirmation({
        isOpen: true,
        customerId,
        customerName,
        appointmentCount,
        projectCount
      })
    } catch (error) {
      console.error('Error getting appointment count:', error)
      setDeleteConfirmation({
        isOpen: true,
        customerId,
        customerName,
        appointmentCount: 0,
        projectCount: 0
      })
    }
  }

  // Handle modal actions
  const handleViewDetails = (customer: Customer) => {
    setSelectedCustomer(customer)
    setShowDetailsModal(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer)
    setShowEditModal(true)
  }

  const handleViewProjects = (customer: Customer) => {
    setSelectedCustomer(customer)
    setShowProjectsModal(true)
  }

  // Handle edit customer save
  const handleEditCustomerSave = async (customerId: string, customerData: CustomerFormData) => {
    try {
      console.log('Updating customer:', customerId, customerData)
      
      // Transform form data to match Customer type
      const customerToUpdate = transformFormDataToCustomer(customerData)
      
      // Use the actual updateCustomer function from the hook
      const updatedCustomer = await updateCustomer(customerId, customerToUpdate)
      
      if (updatedCustomer) {
        console.log('Customer updated successfully:', updatedCustomer)
        // Update the selected customer state to show updated info
        setSelectedCustomer(updatedCustomer)
      } else {
        throw new Error('Failed to update customer')
      }
    } catch (error) {
      console.error('Error updating customer:', error)
      // You might want to show a toast notification here
      alert('Failed to update customer. Please try again.')
      throw error
    }
  }

  const confirmDeleteCustomer = async () => {
    try {
      const success = await deleteCustomer(deleteConfirmation.customerId)
      if (success) {
        console.log('Customer deleted successfully')
        // The useCustomersData hook will automatically update the customers list
      } else {
        console.error('Failed to delete customer')
        alert('Failed to delete customer. Please try again.')
      }
    } catch (error) {
      console.error('Error deleting customer:', error)
      alert('An error occurred while deleting the customer.')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Customers</h1>
          <p className="text-muted-foreground">Manage your client base and preferences</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="secondary" className="text-xs">
            Saturday, July 12, 2025
          </Badge>
          <AccentButton onClick={() => setShowNewCustomerModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Customer
          </AccentButton>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Total Customers',
            value: dashboardStats?.totalCustomers || 0,
            change: '',
            icon: Users,
            trend: 'neutral' as const
          },
          {
            title: 'New Customers',
            value: Math.floor((dashboardStats?.totalCustomers || 0) * 0.15), // Estimate new customers as 15% of total
            change: '+5%',
            icon: ArrowUp,
            trend: 'up' as const
          },
          {
            title: 'High-Value Clients',
            value: Math.floor((dashboardStats?.totalCustomers || 0) * 0.25), // Estimate 25% as high-value
            change: '',
            icon: Star,
            trend: 'neutral' as const
          },
          {
            title: 'Avg Revenue/Customer',
            value: dashboardStats?.totalCustomers && dashboardStats?.totalCustomers > 0 
              ? `$${Math.round(dashboardStats.revenueThisMonth / dashboardStats.totalCustomers).toLocaleString()}`
              : '$0',
            change: '+8%',
            icon: DollarSign,
            trend: 'up' as const
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:border-border/80 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="p-2 rounded-md bg-primary/10">
                    <stat.icon className="h-5 w-5 text-primary" />
                  </div>
                  {stat.change && (
                    <Badge 
                      variant={stat.trend === 'up' ? 'default' : 'secondary'} 
                      className="text-xs"
                    >
                      {stat.change}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <h3 className="text-2xl font-bold text-foreground">{stat.value}</h3>
                  <p className="text-sm text-muted-foreground">{stat.title}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Filters and Search */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader className="pb-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-1 items-center space-x-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search customers..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={preferenceFilter} onValueChange={setPreferenceFilter}>
                  <SelectTrigger className="w-36">
                    <SelectValue placeholder="Preference" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Products</SelectItem>
                    <SelectItem value="blinds">Blinds</SelectItem>
                    <SelectItem value="shutters">Shutters</SelectItem>
                    <SelectItem value="shades">Shades</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="date">Date Added</SelectItem>
                    <SelectItem value="revenue">Revenue</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                
                {selectedCustomers.length > 0 && (
                  <Badge variant="secondary">
                    {selectedCustomers.length} selected
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>
      </motion.div>

      {/* Customer Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b border-border">
                  <tr className="bg-accent/20">
                    <th className="text-left p-4 w-12">
                      <Checkbox
                        checked={selectedCustomers.length === paginatedCustomers.length && paginatedCustomers.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="text-left p-4 font-medium text-foreground">Customer</th>
                    <th className="text-left p-4 font-medium text-foreground">Contact</th>
                    <th className="text-left p-4 font-medium text-foreground">Address</th>
                    <th className="text-left p-4 font-medium text-foreground">Last Project</th>
                    <th className="text-left p-4 font-medium text-foreground">Status</th>
                    <th className="text-left p-4 font-medium text-foreground">Revenue</th>
                    <th className="text-right p-4 w-16"></th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedCustomers.map((customer, index) => {
                    const StatusIcon = getStatusIcon(customer.status)
                    return (
                      <motion.tr
                        key={customer.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="border-b border-border hover:bg-accent/30 transition-colors"
                      >
                        <td className="p-4">                            <Checkbox
                              checked={selectedCustomers.includes(customer.id)}
                              onCheckedChange={() => handleSelectCustomer(customer.id)}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                            />
                        </td>
                        <td className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={customer.avatar || undefined} />
                              <AvatarFallback className="bg-primary/10 text-primary">
                                {customer.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-foreground">{customer.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {customer.projectCount} project{customer.projectCount !== 1 ? 's' : ''}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Mail className="h-3 w-3 mr-2" />
                              {customer.email}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Phone className="h-3 w-3 mr-2" />
                              {customer.phone}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center text-sm text-muted-foreground">
                            <MapPin className="h-3 w-3 mr-2 flex-shrink-0" />
                            <span className="truncate max-w-48">{customer.address}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          {customer.lastProject ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="h-3 w-3 mr-2" />
                              {new Date(customer.lastProject).toLocaleDateString()}
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">No projects</span>
                          )}
                        </td>
                        <td className="p-4">
                          <Badge variant={getStatusBadgeVariant(customer.status)} className="text-xs">
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {customer.status}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <span className="font-medium text-foreground">
                            ${customer.totalRevenue.toLocaleString()}
                          </span>
                        </td>
                        <td className="p-4">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                const actualCustomer = customers.find(c => c.id === customer.id)
                                if (actualCustomer) handleViewDetails(actualCustomer)
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                const actualCustomer = customers.find(c => c.id === customer.id)
                                if (actualCustomer) handleEditCustomer(actualCustomer)
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Customer
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                const actualCustomer = customers.find(c => c.id === customer.id)
                                if (actualCustomer) handleViewProjects(actualCustomer)
                              }}>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Projects
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="text-destructive"
                                onClick={() => handleDeleteCustomer(customer.id, customer.name)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </motion.tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between p-4 border-t border-border">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredCustomers.length)} of {filteredCustomers.length} customers
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className="w-8 h-8 p-0"
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Quick Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
      >
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Top Customers by Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {displayCustomers
                .sort((a, b) => b.totalRevenue - a.totalRevenue)
                .slice(0, 5)
                .map((customer, index) => (
                  <div key={customer.id} className="flex items-center justify-between p-3 rounded-md bg-accent/20">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-muted-foreground w-4">
                        #{index + 1}
                      </span>
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="bg-primary/10 text-primary text-xs">
                          {customer.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium text-foreground">{customer.name}</span>
                    </div>
                    <span className="text-sm font-medium text-foreground">
                      ${customer.totalRevenue.toLocaleString()}
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 rounded-md bg-primary/5 border border-primary/20">
                <p className="text-sm text-foreground font-medium mb-1">Outreach Recommendation</p>
                <p className="text-xs text-muted-foreground">
                  5 inactive clients haven&apos;t been contacted in 90+ days. Consider a follow-up campaign.
                </p>
              </div>
              
              <div className="p-3 rounded-md bg-accent/20">
                <p className="text-sm text-foreground font-medium mb-1">Seasonal Opportunity</p>
                <p className="text-xs text-muted-foreground">
                  12 customers have shown interest in shutters. Summer promotion recommended.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={showNewCustomerModal}
        onClose={() => setShowNewCustomerModal(false)}
        onSave={handleSaveCustomer}
      />

      {/* Edit Customer Modal */}
      <EditCustomerModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        customer={selectedCustomer}
        onSave={handleEditCustomerSave}
      />

      {/* Customer Details Modal */}
      <CustomerDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        customer={selectedCustomer}
      />

      {/* Customer Projects Modal */}
      <CustomerProjectsModal
        isOpen={showProjectsModal}
        onClose={() => setShowProjectsModal(false)}
        customer={selectedCustomer}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={() => setDeleteConfirmation({ isOpen: false, customerId: '', customerName: '', appointmentCount: 0, projectCount: 0 })}
        onConfirm={confirmDeleteCustomer}
        title="Delete Customer"
        description={(() => {
          const { customerName, projectCount = 0, appointmentCount = 0 } = deleteConfirmation
          const hasProjects = projectCount > 0
          const hasAppointments = appointmentCount > 0
          
          let message = `Are you sure you want to delete customer "${customerName}"?`
          
          if (hasProjects || hasAppointments) {
            message += ' This will also permanently delete:'
            const items = []
            
            if (hasProjects) {
              items.push(`${projectCount} project${projectCount === 1 ? '' : 's'}`)
            }
            
            if (hasAppointments) {
              items.push(`${appointmentCount} appointment${appointmentCount === 1 ? '' : 's'}`)
            }
            
            message += ` ${items.join(' and ')}.`
          }
          
          message += ' This action cannot be undone.'
          return message
        })()}
        confirmText="Delete Customer"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  )
}
