import { supabase } from '@/lib/supabase/client'
import type { User, UserRole, UserProfile } from '@/types'

export class UsersService {
  private supabase = supabase

  async getUsers(): Promise<User[]> {
    try {
      console.log('Fetching users from database...')
      
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          first_name,
          last_name,
          phone,
          avatar,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false })

      console.log('Users query response:', { data, error })

      if (error) {
        console.error('Failed to fetch users:', error)
        return []
      }

      const mappedUsers = (data || []).map((user) => ({
        id: user.id,
        email: user.email,
        role: user.role as UserRole,
        profile: {
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone || undefined,
          avatar: user.avatar || undefined,
        },
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      }))

      console.log('Mapped users:', mappedUsers)
      return mappedUsers
    } catch (error) {
      console.error('Users service error:', error)
      return []
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          first_name,
          last_name,
          phone,
          avatar,
          created_at,
          updated_at
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Failed to fetch user:', error)
        return null
      }

      return {
        id: data.id,
        email: data.email,
        role: data.role as UserRole,
        profile: {
          firstName: data.first_name,
          lastName: data.last_name,
          phone: data.phone || undefined,
          avatar: data.avatar || undefined,
        },
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      }
    } catch (error) {
      console.error('Get user by ID service error:', error)
      return null
    }
  }

  async getUsersByRole(role: UserRole): Promise<User[]> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          first_name,
          last_name,
          phone,
          avatar,
          created_at,
          updated_at
        `)
        .eq('role', role)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to fetch users by role:', error)
        return []
      }

      return (data || []).map((user) => ({
        id: user.id,
        email: user.email,
        role: user.role as UserRole,
        profile: {
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone || undefined,
          avatar: user.avatar || undefined,
        },
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      }))
    } catch (error) {
      console.error('Get users by role service error:', error)
      return []
    }
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .insert({
          email: userData.email,
          role: userData.role,
          first_name: userData.profile.firstName,
          last_name: userData.profile.lastName,
          phone: userData.profile.phone,
          avatar: userData.profile.avatar,
        })
        .select()
        .single()

      if (error) {
        console.error('Failed to create user:', error)
        return null
      }

      return {
        id: data.id,
        email: data.email,
        role: data.role as UserRole,
        profile: {
          firstName: data.first_name,
          lastName: data.last_name,
          phone: data.phone || undefined,
          avatar: data.avatar || undefined,
        },
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      }
    } catch (error) {
      console.error('Create user service error:', error)
      return null
    }
  }

  async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (userData.email !== undefined) updateData.email = userData.email
      if (userData.role !== undefined) updateData.role = userData.role
      if (userData.profile?.firstName !== undefined) updateData.first_name = userData.profile.firstName
      if (userData.profile?.lastName !== undefined) updateData.last_name = userData.profile.lastName
      if (userData.profile?.phone !== undefined) updateData.phone = userData.profile.phone
      if (userData.profile?.avatar !== undefined) updateData.avatar = userData.profile.avatar

      const { data, error } = await this.supabase
        .from('users')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Failed to update user:', error)
        return null
      }

      return {
        id: data.id,
        email: data.email,
        role: data.role as UserRole,
        profile: {
          firstName: data.first_name,
          lastName: data.last_name,
          phone: data.phone || undefined,
          avatar: data.avatar || undefined,
        },
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      }
    } catch (error) {
      console.error('Update user service error:', error)
      return null
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      // Check if user has any related records that would prevent deletion
      const { data: projects } = await this.supabase
        .from('projects')
        .select('id')
        .eq('installer_id', id)
        .limit(1)

      if (projects && projects.length > 0) {
        console.error('Cannot delete user: has related projects')
        return false
      }

      const { data: tasks } = await this.supabase
        .from('installation_tasks')
        .select('id')
        .eq('assigned_to', id)
        .limit(1)

      if (tasks && tasks.length > 0) {
        console.error('Cannot delete user: has related installation tasks')
        return false
      }

      const { error } = await this.supabase
        .from('users')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete user:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete user service error:', error)
      return false
    }
  }

  async getUserStats() {
    try {
      // Get total users count
      const { count: totalUsers, error: totalError } = await this.supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      if (totalError) {
        console.error('Failed to get total users count:', totalError)
        return {
          totalUsers: 0,
          usersByRole: {},
          activeUsers: 0,
          recentUsers: 0
        }
      }

      // Get users by role
      const { data: roleData, error: roleError } = await this.supabase
        .from('users')
        .select('role')

      const usersByRole = roleData?.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1
        return acc
      }, {} as Record<string, number>) || {}

      // Get recent users (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { count: recentUsers, error: recentError } = await this.supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', thirtyDaysAgo.toISOString())

      return {
        totalUsers: totalUsers || 0,
        usersByRole,
        activeUsers: totalUsers || 0, // All users are considered active for now
        recentUsers: recentUsers || 0
      }
    } catch (error) {
      console.error('Get user stats service error:', error)
      return {
        totalUsers: 0,
        usersByRole: {},
        activeUsers: 0,
        recentUsers: 0
      }
    }
  }

  async searchUsers(query: string): Promise<User[]> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          email,
          role,
          first_name,
          last_name,
          phone,
          avatar,
          created_at,
          updated_at
        `)
        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to search users:', error)
        return []
      }

      return (data || []).map((user) => ({
        id: user.id,
        email: user.email,
        role: user.role as UserRole,
        profile: {
          firstName: user.first_name,
          lastName: user.last_name,
          phone: user.phone || undefined,
          avatar: user.avatar || undefined,
        },
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      }))
    } catch (error) {
      console.error('Search users service error:', error)
      return []
    }
  }
}

export const usersService = new UsersService() 