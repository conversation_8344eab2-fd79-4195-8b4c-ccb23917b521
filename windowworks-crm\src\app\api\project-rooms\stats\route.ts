import { NextResponse } from 'next/server'
import { projectRoomsService } from '@/lib/services/project-rooms-service'

export async function GET() {
  try {
    const stats = await projectRoomsService.getProjectRoomStats()
    
    return NextResponse.json({
      status: 'success',
      data: stats
    })
    
  } catch (error) {
    console.error('Project room stats API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch project room statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 