'use client'

import React, { useState, useEffect } from 'react'
import { 
  Package,
  Calendar,
  DollarSign,
  Clock,
  User,
  MapPin,
  Star,
  MoreHorizontal,
  Eye,
  Edit,
  X,
  RefreshCw,
  AlertCircle,
  Plus,
  TrendingUp
} from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useTheme } from '@/contexts/theme-context'
import type { Customer, Project } from '@/types'

interface CustomerProjectsModalProps {
  isOpen: boolean
  onClose: () => void
  customer: Customer | null
}

// Mock projects data - in real app, this would come from the API
const getMockProjects = (customerId: string): Project[] => [
  {
    id: '1',
    customerId,
    title: 'Living Room Window Treatments',
    description: 'Custom blinds installation for main living area windows',
    status: 'in-progress',
    priority: 'high',
    estimatedCost: 2500,
    actualCost: 2750,
    estimatedDuration: 5,
    startDate: '2024-01-15',
    completionDate: undefined,
    installerId: '123',
    rooms: [],
    notes: 'Customer prefers white blinds with cordless operation',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    createdBy: 'user123'
  },
  {
    id: '2',
    customerId,
    title: 'Bedroom Shutters Installation',
    description: 'Plantation shutters for master bedroom',
    status: 'completed',
    priority: 'medium',
    estimatedCost: 1800,
    actualCost: 1850,
    estimatedDuration: 3,
    startDate: '2023-12-01',
    completionDate: '2023-12-03',
    installerId: '456',
    rooms: [],
    notes: 'Premium white shutters with 3.5" louvers',
    createdAt: '2023-11-25T09:00:00Z',
    updatedAt: '2023-12-03T16:00:00Z',
    createdBy: 'user123'
  }
]

const statusColors = {
  'draft': 'bg-gray-100 text-gray-800',
  'quoted': 'bg-blue-100 text-blue-800',
  'approved': 'bg-green-100 text-green-800',
  'scheduled': 'bg-purple-100 text-purple-800',
  'in-progress': 'bg-yellow-100 text-yellow-800',
  'completed': 'bg-emerald-100 text-emerald-800',
  'cancelled': 'bg-red-100 text-red-800',
  'on-hold': 'bg-orange-100 text-orange-800'
}

const priorityColors = {
  'low': 'bg-gray-100 text-gray-600',
  'medium': 'bg-blue-100 text-blue-600',
  'high': 'bg-orange-100 text-orange-600',
  'urgent': 'bg-red-100 text-red-600'
}

export function CustomerProjectsModal({ isOpen, onClose, customer }: CustomerProjectsModalProps) {
  const { accentColor } = useTheme()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(false)

  // Load projects when customer changes
  useEffect(() => {
    if (customer) {
      setLoading(true)
      // Simulate API call
      setTimeout(() => {
        const mockProjects = getMockProjects(customer.id)
        setProjects(mockProjects)
        setLoading(false)
      }, 500)
    }
  }, [customer])

  if (!customer) return null

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getTotalRevenue = () => {
    return projects
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + (p.actualCost || p.estimatedCost), 0)
  }

  const getProjectStats = () => {
    return {
      total: projects.length,
      completed: projects.filter(p => p.status === 'completed').length,
      inProgress: projects.filter(p => p.status === 'in-progress').length,
      revenue: getTotalRevenue()
    }
  }

  const stats = getProjectStats()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl sm:max-w-6xl max-h-[80vh] overflow-hidden p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src="" />
                  <AvatarFallback className="text-lg font-semibold" style={{ backgroundColor: accentColor, color: 'white' }}>
                    {getInitials(customer.firstName, customer.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <DialogTitle className="text-xl font-semibold text-slate-700">
                    {customer.firstName} {customer.lastName} - Projects
                  </DialogTitle>
                  <p className="text-sm text-slate-500">
                    {stats.total} project{stats.total !== 1 ? 's' : ''} • {formatCurrency(stats.revenue)} total revenue
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-200"
                  style={{ borderColor: accentColor, color: accentColor }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Project
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </DialogHeader>

          {/* Stats Overview */}
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Projects</p>
                      <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                    </div>
                    <Package className="h-8 w-8 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Completed</p>
                      <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                    </div>
                    <Star className="h-8 w-8 text-green-400" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">In Progress</p>
                      <p className="text-2xl font-bold text-yellow-600">{stats.inProgress}</p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-400" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Revenue</p>
                      <p className="text-2xl font-bold text-blue-600">{formatCurrency(stats.revenue)}</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-blue-400" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                  <p className="text-muted-foreground">Loading projects...</p>
                </div>
              </div>
            ) : projects.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Yet</h3>
                  <p className="text-gray-500 mb-4">This customer doesn't have any projects yet.</p>
                  <Button
                    className="text-white"
                    style={{ backgroundColor: accentColor }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Project
                  </Button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {projects.map((project) => (
                  <Card key={project.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <h3 className="text-lg font-semibold text-gray-900">{project.title}</h3>
                            <Badge 
                              className={`text-xs ${statusColors[project.status as keyof typeof statusColors]}`}
                            >
                              {project.status.replace('-', ' ').toUpperCase()}
                            </Badge>
                            <Badge 
                              variant="outline"
                              className={`text-xs ${priorityColors[project.priority as keyof typeof priorityColors]}`}
                            >
                              {project.priority.toUpperCase()}
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-4">{project.description}</p>
                          
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <div>
                                <p className="font-medium text-gray-600">Start Date</p>
                                <p className="text-gray-800">{formatDate(project.startDate)}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <div>
                                <p className="font-medium text-gray-600">Completion</p>
                                <p className="text-gray-800">{formatDate(project.completionDate)}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              <div>
                                <p className="font-medium text-gray-600">Estimated Cost</p>
                                <p className="text-gray-800">{formatCurrency(project.estimatedCost)}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <DollarSign className="h-4 w-4 text-gray-400" />
                              <div>
                                <p className="font-medium text-gray-600">Actual Cost</p>
                                <p className="text-gray-800">
                                  {project.actualCost ? formatCurrency(project.actualCost) : 'TBD'}
                                </p>
                              </div>
                            </div>
                          </div>

                          {project.notes && (
                            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                              <p className="text-sm text-gray-700">{project.notes}</p>
                            </div>
                          )}
                        </div>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Project
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {projects.length} project{projects.length !== 1 ? 's' : ''} total
              </div>
              <Button
                onClick={onClose}
                className="text-white"
                style={{ 
                  backgroundColor: accentColor,
                  borderColor: accentColor
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 