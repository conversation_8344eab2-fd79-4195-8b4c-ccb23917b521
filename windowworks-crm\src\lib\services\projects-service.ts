import { supabase } from '@/lib/supabase/client'

export interface Project {
  id: string
  title: string
  customer: {
    id: string
    name: string
    email: string
    phone: string
  }
  status: 'active' | 'completed' | 'cancelled' | 'pending'
  installer: {
    id: string
    name: string
    avatar: string | null
  } | null
  dueDate: string | null
  productType: string[]
  description: string
  address: string
  totalValue: number
  createdAt: string
  completionTime?: number
}

export class ProjectsService {
  private supabase = supabase

  async getProjects(): Promise<Project[]> {
    try {
      const { data, error } = await this.supabase
        .from('projects')
        .select(`
          id,
          title,
          description,
          status,
          estimated_cost,
          actual_cost,
          created_at,
          start_date,
          completion_date,
          customer_id,
          installer_id,
          customers (
            id,
            first_name,
            last_name,
            email,
            phone,
            address_street,
            address_city,
            address_state,
            address_zip_code
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to fetch projects:', error)
        return []
      }

      return (data || []).map((project) => ({
        id: project.id,
        title: project.title || 'Untitled Project',
        customer: {
          id: project.customers?.id || '',
          name: project.customers 
            ? `${project.customers.first_name} ${project.customers.last_name}`
            : 'Unknown Customer',
          email: project.customers?.email || '',
          phone: project.customers?.phone || ''
        },
        status: this.mapStatus(project.status),
        installer: project.installer_id ? {
          id: project.installer_id,
          name: 'Installer Name', // TODO: Fetch installer details from users table
          avatar: null
        } : null,
        dueDate: project.completion_date || project.start_date || null,
        productType: this.extractProductTypes(project.title, project.description),
        description: project.description || '',
        address: this.getCustomerAddress(project.customers),
        totalValue: Number(project.estimated_cost) || Number(project.actual_cost) || 0,
        createdAt: project.created_at,
        completionTime: this.calculateCompletionTime(project.status, project.created_at, project.completion_date)
      }))
    } catch (error) {
      console.error('Projects service error:', error)
      return []
    }
  }

  async getProjectStats() {
    try {
      const { data: statsData, error } = await this.supabase
        .from('dashboard_stats')
        .select('*')
        .single()

      if (error) {
        console.error('Failed to fetch project stats:', error)
        return {
          totalProjects: 0,
          activeProjects: 0,
          completedProjects: 0,
          overdueProjects: 0,
          averageCompletionTime: 0
        }
      }

      // Calculate overdue projects
      const { data: overdueData, error: overdueError } = await this.supabase
        .from('projects')
        .select('id')
        .eq('status', 'active')
        .lt('due_date', new Date().toISOString())

      const overdueCount = overdueError ? 0 : (overdueData?.length || 0)

      // Calculate average completion time
      const { data: completedProjects, error: completedError } = await this.supabase
        .from('projects')
        .select('created_at, updated_at')
        .eq('status', 'completed')

      let avgCompletionDays = 0
      if (!completedError && completedProjects && completedProjects.length > 0) {
        const totalDays = completedProjects.reduce((sum, project) => {
          const created = new Date(project.created_at)
          const completed = new Date(project.updated_at)
          const days = Math.ceil((completed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
          return sum + days
        }, 0)
        avgCompletionDays = Math.round(totalDays / completedProjects.length)
      }

      return {
        totalProjects: statsData.total_projects || 0,
        activeProjects: statsData.active_projects || 0,
        completedProjects: statsData.completed_projects || 0,
        overdueProjects: overdueCount,
        averageCompletionTime: avgCompletionDays
      }
    } catch (error) {
      console.error('Project stats service error:', error)
      return {
        totalProjects: 0,
        activeProjects: 0,
        completedProjects: 0,
        overdueProjects: 0,
        averageCompletionTime: 0
      }
    }
  }

  async createProject(projectData: {
    title: string
    description: string
    customerId: string
    estimatedCost?: number
    startDate?: string
    installerId?: string
  }): Promise<Project | null> {
    try {
      const { data, error } = await this.supabase
        .from('projects')
        .insert({
          title: projectData.title,
          description: projectData.description,
          customer_id: projectData.customerId,
          estimated_cost: projectData.estimatedCost,
          start_date: projectData.startDate,
          installer_id: projectData.installerId,
          status: 'pending',
          created_by: 'system' // TODO: Replace with actual user ID when auth is implemented
        })
        .select(`
          id,
          title,
          description,
          status,
          estimated_cost,
          actual_cost,
          created_at,
          start_date,
          completion_date,
          customer_id,
          installer_id,
          customers (
            id,
            first_name,
            last_name,
            email,
            phone,
            address_street,
            address_city,
            address_state,
            address_zip_code
          )
        `)
        .single()

      if (error) {
        console.error('Failed to create project:', error)
        return null
      }

      return {
        id: data.id,
        title: data.title || 'Untitled Project',
        customer: {
          id: data.customers?.id || '',
          name: data.customers 
            ? `${data.customers.first_name} ${data.customers.last_name}`
            : 'Unknown Customer',
          email: data.customers?.email || '',
          phone: data.customers?.phone || ''
        },
        status: this.mapStatus(data.status),
        installer: data.installer_id ? {
          id: data.installer_id,
          name: 'Installer Name',
          avatar: null
        } : null,
        dueDate: data.completion_date || data.start_date || null,
        productType: this.extractProductTypes(data.title, data.description),
        description: data.description || '',
        address: this.getCustomerAddress(data.customers),
        totalValue: Number(data.estimated_cost) || Number(data.actual_cost) || 0,
        createdAt: data.created_at,
        completionTime: this.calculateCompletionTime(data.status, data.created_at, data.completion_date)
      }
    } catch (error) {
      console.error('Create project service error:', error)
      return null
    }
  }

  async updateProject(id: string, projectData: Partial<{
    title: string
    description: string
    status: string
    estimatedCost: number
    actualCost: number
    startDate: string
    completionDate: string
    installerId: string
  }>): Promise<Project | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (projectData.title !== undefined) updateData.title = projectData.title
      if (projectData.description !== undefined) updateData.description = projectData.description
      if (projectData.status !== undefined) updateData.status = projectData.status
      if (projectData.estimatedCost !== undefined) updateData.estimated_cost = projectData.estimatedCost
      if (projectData.actualCost !== undefined) updateData.actual_cost = projectData.actualCost
      if (projectData.startDate !== undefined) updateData.start_date = projectData.startDate
      if (projectData.completionDate !== undefined) updateData.completion_date = projectData.completionDate
      if (projectData.installerId !== undefined) updateData.installer_id = projectData.installerId

      const { data, error } = await this.supabase
        .from('projects')
        .update(updateData)
        .eq('id', id)
        .select(`
          id,
          title,
          description,
          status,
          estimated_cost,
          actual_cost,
          created_at,
          start_date,
          completion_date,
          customer_id,
          installer_id,
          customers (
            id,
            first_name,
            last_name,
            email,
            phone,
            address_street,
            address_city,
            address_state,
            address_zip_code
          )
        `)
        .single()

      if (error) {
        console.error('Failed to update project:', error)
        return null
      }

      return {
        id: data.id,
        title: data.title || 'Untitled Project',
        customer: {
          id: data.customers?.id || '',
          name: data.customers 
            ? `${data.customers.first_name} ${data.customers.last_name}`
            : 'Unknown Customer',
          email: data.customers?.email || '',
          phone: data.customers?.phone || ''
        },
        status: this.mapStatus(data.status),
        installer: data.installer_id ? {
          id: data.installer_id,
          name: 'Installer Name',
          avatar: null
        } : null,
        dueDate: data.completion_date || data.start_date || null,
        productType: this.extractProductTypes(data.title, data.description),
        description: data.description || '',
        address: this.getCustomerAddress(data.customers),
        totalValue: Number(data.estimated_cost) || Number(data.actual_cost) || 0,
        createdAt: data.created_at,
        completionTime: this.calculateCompletionTime(data.status, data.created_at, data.completion_date)
      }
    } catch (error) {
      console.error('Update project service error:', error)
      return null
    }
  }

  async deleteProject(id: string): Promise<boolean> {
    try {
      // First, check if there are any related installation tasks
      const { data: tasks } = await this.supabase
        .from('installation_tasks')
        .select('id')
        .eq('project_id', id)

      // If there are tasks, delete them first (or prevent deletion)
      if (tasks && tasks.length > 0) {
        const { error: tasksError } = await this.supabase
          .from('installation_tasks')
          .delete()
          .eq('project_id', id)

        if (tasksError) {
          console.error('Failed to delete related tasks:', tasksError)
          return false
        }
      }

      const { error } = await this.supabase
        .from('projects')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete project:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete project service error:', error)
      return false
    }
  }

  private mapStatus(status: string): 'active' | 'completed' | 'cancelled' | 'pending' {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'in-progress':
      case 'in_progress':
        return 'active'
      case 'completed':
      case 'finished':
        return 'completed'
      case 'cancelled':
      case 'canceled':
        return 'cancelled'
      case 'pending':
      case 'scheduled':
        return 'pending'
      default:
        return 'pending'
    }
  }

  private extractProductTypes(title: string, description: string): string[] {
    const text = `${title} ${description}`.toLowerCase()
    const types: string[] = []
    
    if (text.includes('shutter')) types.push('Shutters')
    if (text.includes('blind')) types.push('Blinds')
    if (text.includes('shade')) types.push('Shades')
    if (text.includes('curtain')) types.push('Curtains')
    if (text.includes('window')) types.push('Windows')
    if (text.includes('door')) types.push('Doors')
    
    return types.length > 0 ? types : ['General']
  }

  private getCustomerAddress(customer: { address_street?: string; address_city?: string; address_state?: string; address_zip_code?: string } | null): string {
    if (!customer) return 'Address not available'
    
    const parts = [
      customer.address_street,
      customer.address_city,
      customer.address_state,
      customer.address_zip_code
    ].filter(Boolean)
    
    return parts.length > 0 ? parts.join(', ') : 'Address not available'
  }

  private calculateCompletionTime(status: string, createdAt: string, completionDate: string | null): number | undefined {
    if (status !== 'completed' || !completionDate) return undefined
    
    const created = new Date(createdAt)
    const completed = new Date(completionDate)
    
    return Math.ceil((completed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
  }
}

export const projectsService = new ProjectsService()
