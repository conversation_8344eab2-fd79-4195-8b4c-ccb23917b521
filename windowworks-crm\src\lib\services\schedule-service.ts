import { supabase } from '@/lib/supabase/client'

export interface Appointment {
  id: string
  title: string
  date: string
  time: string
  startTime: string
  endTime?: string
  duration: number
  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'cancelled'
  customer: {
    id: string
    name: string
    email: string
    phone: string
  }
  installer: {
    id: string
    name: string
    avatar?: string | null
  } | null
  location: string
  productTypes: string[]
  notes?: string
  projectId: string
}

export interface Installer {
  id: string
  name: string
  avatar?: string | null
  availability: 'available' | 'busy' | 'unavailable'
  utilization: number
  activeAppointments: number
}

export interface ScheduleStats {
  upcomingAppointments: number
  installerUtilization: number
  conflicts: number
  averageJobDuration: number
}

export class ScheduleService {
  private supabase = supabase

  async getAppointments(startDate?: Date, endDate?: Date): Promise<Appointment[]> {
    try {
      let query = this.supabase
        .from('installation_tasks')
        .select(`
          id,
          title,
          description,
          status,
          estimated_duration,
          actual_duration,
          start_time,
          end_time,
          location_street,
          location_city,
          location_state,
          notes,
          project_id,
          projects (
            id,
            title,
            customer_id,
            customers (
              id,
              first_name,
              last_name,
              email,
              phone
            )
          ),
          assigned_to,
          users (
            id,
            first_name,
            last_name
          )
        `)
        .order('start_time', { ascending: true })

      if (startDate) {
        query = query.gte('start_time', startDate.toISOString())
      }
      if (endDate) {
        query = query.lte('start_time', endDate.toISOString())
      }

      const { data, error } = await query

      if (error) {
        console.error('Failed to fetch appointments:', error)
        return []
      }

      return (data || []).map((task) => ({
        id: task.id,
        title: task.title || 'Untitled Task',
        date: task.start_time ? new Date(task.start_time).toISOString().split('T')[0] : '',
        time: task.start_time ? this.formatTime(task.start_time) : '',
        startTime: task.start_time ? this.formatTime(task.start_time) : '',
        endTime: task.end_time ? this.formatTime(task.end_time) : undefined,
        duration: task.estimated_duration || 120,
        status: this.mapTaskStatus(task.status),
        customer: {
          id: task.projects?.customers?.id || '',
          name: task.projects?.customers 
            ? `${task.projects.customers.first_name} ${task.projects.customers.last_name}`
            : 'Unknown Customer',
          email: task.projects?.customers?.email || '',
          phone: task.projects?.customers?.phone || ''
        },
        installer: task.users ? {
          id: task.users.id,
          name: `${task.users.first_name} ${task.users.last_name}`,
          avatar: null
        } : null,
        location: this.formatLocation(task.location_street, task.location_city, task.location_state),
        productTypes: this.extractProductTypes(task.title, task.description),
        notes: task.notes || '',
        projectId: task.project_id
      }))
    } catch (error) {
      console.error('Schedule service error:', error)
      return []
    }
  }

  async getInstallers(): Promise<Installer[]> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          id,
          first_name,
          last_name,
          email,
          role
        `)
        .eq('role', 'installer')

      if (error) {
        console.error('Failed to fetch installers:', error)
        return []
      }

      // Get active appointments for each installer
      const installersWithStats = await Promise.all(
        (data || []).map(async (user) => {
          const { data: appointments } = await this.supabase
            .from('installation_tasks')
            .select('id, status, start_time')
            .eq('assigned_to', user.id)
            .in('status', ['pending', 'in-progress'])

          const activeCount = appointments?.length || 0
          const utilization = Math.min(100, activeCount * 25) // Rough calculation

          return {
            id: user.id,
            name: `${user.first_name} ${user.last_name}`,
            avatar: null,
            availability: activeCount >= 4 ? 'busy' as const : 'available' as const,
            utilization,
            activeAppointments: activeCount
          }
        })
      )

      return installersWithStats
    } catch (error) {
      console.error('Installers service error:', error)
      return []
    }
  }

  async getScheduleStats(): Promise<ScheduleStats> {
    try {
      // Get upcoming appointments (next 7 days)
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)

      const { data: upcomingData } = await this.supabase
        .from('installation_tasks')
        .select('id')
        .gte('start_time', new Date().toISOString())
        .lte('start_time', nextWeek.toISOString())

      // Get installer utilization
      const installers = await this.getInstallers()
      const avgUtilization = installers.length > 0 
        ? Math.round(installers.reduce((sum, inst) => sum + inst.utilization, 0) / installers.length)
        : 0

      // Get conflicts (overlapping appointments for same installer)
      const { data: conflictsData } = await this.supabase
        .from('installation_tasks')
        .select('assigned_to, start_time, end_time')
        .not('assigned_to', 'is', null)
        .gte('start_time', new Date().toISOString())

      let conflicts = 0
      if (conflictsData) {
        // Simple conflict detection - group by installer and check for overlaps
        const installerTasks = new Map()
        conflictsData.forEach(task => {
          if (!installerTasks.has(task.assigned_to)) {
            installerTasks.set(task.assigned_to, [])
          }
          installerTasks.get(task.assigned_to).push(task)
        })

        installerTasks.forEach(tasks => {
          for (let i = 0; i < tasks.length - 1; i++) {
            for (let j = i + 1; j < tasks.length; j++) {
              if (this.tasksOverlap(tasks[i], tasks[j])) {
                conflicts++
              }
            }
          }
        })
      }

      // Get average job duration
      const { data: completedTasks } = await this.supabase
        .from('installation_tasks')
        .select('estimated_duration, actual_duration')
        .eq('status', 'completed')

      let avgDuration = 4 // Default 4 hours
      if (completedTasks && completedTasks.length > 0) {
        const totalDuration = completedTasks.reduce((sum, task) => 
          sum + (task.actual_duration || task.estimated_duration || 240), 0)
        avgDuration = Math.round((totalDuration / completedTasks.length) / 60) // Convert to hours
      }

      return {
        upcomingAppointments: upcomingData?.length || 0,
        installerUtilization: avgUtilization,
        conflicts,
        averageJobDuration: avgDuration
      }
    } catch (error) {
      console.error('Schedule stats service error:', error)
      return {
        upcomingAppointments: 0,
        installerUtilization: 0,
        conflicts: 0,
        averageJobDuration: 4
      }
    }
  }

  async createAppointment(appointmentData: {
    title: string
    startTime: string
    endTime?: string
    duration?: number
    projectId: string
    installerId?: string
    locationStreet?: string
    locationCity?: string
    locationState?: string
    notes?: string
  }): Promise<Appointment | null> {
    try {
      const { data, error } = await this.supabase
        .from('installation_tasks')
        .insert({
          title: appointmentData.title,
          description: appointmentData.title, // Use title for description since title field exists
          start_time: appointmentData.startTime,
          end_time: appointmentData.endTime,
          estimated_duration: appointmentData.duration || 120,
          project_id: appointmentData.projectId,
          assigned_to: appointmentData.installerId,
          location_street: appointmentData.locationStreet || '',
          location_city: appointmentData.locationCity || '',
          location_state: appointmentData.locationState || '',
          location_zip_code: '00000', // Default value since it's required
          notes: appointmentData.notes,
          status: 'pending'
        })
        .select(`
          id,
          title,
          description,
          status,
          estimated_duration,
          actual_duration,
          start_time,
          end_time,
          location_street,
          location_city,
          location_state,
          notes,
          project_id,
          projects (
            id,
            title,
            customer_id,
            customers (
              id,
              first_name,
              last_name,
              email,
              phone
            )
          ),
          assigned_to,
          users (
            id,
            first_name,
            last_name
          )
        `)
        .single()

      if (error) {
        console.error('Failed to create appointment:', error)
        return null
      }

      return {
        id: data.id,
        title: data.title || 'Untitled Task',
        date: data.start_time ? new Date(data.start_time).toISOString().split('T')[0] : '',
        time: data.start_time ? this.formatTime(data.start_time) : '',
        startTime: data.start_time ? this.formatTime(data.start_time) : '',
        endTime: data.end_time ? this.formatTime(data.end_time) : undefined,
        duration: data.estimated_duration || 120,
        status: this.mapTaskStatus(data.status),
        customer: {
          id: data.projects?.customers?.id || '',
          name: data.projects?.customers 
            ? `${data.projects.customers.first_name} ${data.projects.customers.last_name}`
            : 'Unknown Customer',
          email: data.projects?.customers?.email || '',
          phone: data.projects?.customers?.phone || ''
        },
        installer: data.users ? {
          id: data.users.id,
          name: `${data.users.first_name} ${data.users.last_name}`,
          avatar: null
        } : null,
        location: this.formatLocation(data.location_street, data.location_city, data.location_state),
        productTypes: this.extractProductTypes(data.title, data.description),
        notes: data.notes || '',
        projectId: data.project_id
      }
    } catch (error) {
      console.error('Create appointment service error:', error)
      return null
    }
  }

  async updateAppointment(id: string, appointmentData: Partial<{
    title: string
    startTime: string
    endTime: string
    duration: number
    status: string
    installerId: string
    locationStreet: string
    locationCity: string
    locationState: string
    notes: string
    actualDuration: number
  }>): Promise<Appointment | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (appointmentData.title !== undefined) updateData.title = appointmentData.title
      if (appointmentData.startTime !== undefined) updateData.start_time = appointmentData.startTime
      if (appointmentData.endTime !== undefined) updateData.end_time = appointmentData.endTime
      if (appointmentData.duration !== undefined) updateData.estimated_duration = appointmentData.duration
      if (appointmentData.status !== undefined) updateData.status = appointmentData.status
      if (appointmentData.installerId !== undefined) updateData.assigned_to = appointmentData.installerId
      if (appointmentData.locationStreet !== undefined) updateData.location_street = appointmentData.locationStreet
      if (appointmentData.locationCity !== undefined) updateData.location_city = appointmentData.locationCity
      if (appointmentData.locationState !== undefined) updateData.location_state = appointmentData.locationState
      if (appointmentData.notes !== undefined) updateData.notes = appointmentData.notes
      if (appointmentData.actualDuration !== undefined) updateData.actual_duration = appointmentData.actualDuration

      const { data, error } = await this.supabase
        .from('installation_tasks')
        .update(updateData)
        .eq('id', id)
        .select(`
          id,
          title,
          description,
          status,
          estimated_duration,
          actual_duration,
          start_time,
          end_time,
          location_street,
          location_city,
          location_state,
          notes,
          project_id,
          projects (
            id,
            title,
            customer_id,
            customers (
              id,
              first_name,
              last_name,
              email,
              phone
            )
          ),
          assigned_to,
          users (
            id,
            first_name,
            last_name
          )
        `)
        .single()

      if (error) {
        console.error('Failed to update appointment:', error)
        return null
      }

      return {
        id: data.id,
        title: data.title || 'Untitled Task',
        date: data.start_time ? new Date(data.start_time).toISOString().split('T')[0] : '',
        time: data.start_time ? this.formatTime(data.start_time) : '',
        startTime: data.start_time ? this.formatTime(data.start_time) : '',
        endTime: data.end_time ? this.formatTime(data.end_time) : undefined,
        duration: data.estimated_duration || 120,
        status: this.mapTaskStatus(data.status),
        customer: {
          id: data.projects?.customers?.id || '',
          name: data.projects?.customers 
            ? `${data.projects.customers.first_name} ${data.projects.customers.last_name}`
            : 'Unknown Customer',
          email: data.projects?.customers?.email || '',
          phone: data.projects?.customers?.phone || ''
        },
        installer: data.users ? {
          id: data.users.id,
          name: `${data.users.first_name} ${data.users.last_name}`,
          avatar: null
        } : null,
        location: this.formatLocation(data.location_street, data.location_city, data.location_state),
        productTypes: this.extractProductTypes(data.title, data.description),
        notes: data.notes || '',
        projectId: data.project_id
      }
    } catch (error) {
      console.error('Update appointment service error:', error)
      return null
    }
  }

  async deleteAppointment(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('installation_tasks')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete appointment:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete appointment service error:', error)
      return false
    }
  }

  private mapTaskStatus(status: string): 'pending' | 'assigned' | 'in-progress' | 'completed' | 'cancelled' {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'pending'
      case 'in-progress':
        return 'in-progress'
      case 'completed':
        return 'completed'
      case 'cancelled':
        return 'cancelled'
      default:
        return 'assigned'
    }
  }

  private formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  private formatLocation(street?: string, city?: string, state?: string): string {
    const parts = [street, city, state].filter(Boolean)
    return parts.length > 0 ? parts.join(', ') : 'Location TBD'
  }

  private extractProductTypes(title?: string, description?: string): string[] {
    const text = `${title || ''} ${description || ''}`.toLowerCase()
    const types: string[] = []
    
    if (text.includes('shutter')) types.push('Shutters')
    if (text.includes('blind')) types.push('Blinds')
    if (text.includes('shade')) types.push('Shades')
    if (text.includes('curtain')) types.push('Curtains')
    if (text.includes('window')) types.push('Windows')
    if (text.includes('door')) types.push('Doors')
    
    return types.length > 0 ? types : ['Installation']
  }

  private tasksOverlap(task1: { start_time: string; end_time?: string }, task2: { start_time: string; end_time?: string }): boolean {
    if (!task1.start_time || !task2.start_time) return false
    
    const start1 = new Date(task1.start_time)
    const end1 = task1.end_time ? new Date(task1.end_time) : new Date(start1.getTime() + 2 * 60 * 60 * 1000) // Default 2 hours
    const start2 = new Date(task2.start_time)
    const end2 = task2.end_time ? new Date(task2.end_time) : new Date(start2.getTime() + 2 * 60 * 60 * 1000)
    
    return start1 < end2 && start2 < end1
  }
}

export const scheduleService = new ScheduleService()
