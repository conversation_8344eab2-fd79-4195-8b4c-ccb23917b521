import { NextRequest, NextResponse } from 'next/server'
import { projectRoomsService } from '@/lib/services/project-rooms-service'

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const projectId = url.searchParams.get('projectId')
    const search = url.searchParams.get('search')
    const id = url.searchParams.get('id')

    // Get single project room by ID
    if (id) {
      const room = await projectRoomsService.getProjectRoomById(id)
      if (!room) {
        return NextResponse.json({
          status: 'error',
          message: 'Project room not found'
        }, { status: 404 })
      }
      return NextResponse.json({
        status: 'success',
        data: room
      })
    }

    // Search project rooms
    if (search) {
      const rooms = await projectRoomsService.searchProjectRooms(search)
      return NextResponse.json({
        status: 'success',
        data: rooms
      })
    }

    // Get project rooms by project
    if (projectId) {
      const rooms = await projectRoomsService.getProjectRoomsByProject(projectId)
      return NextResponse.json({
        status: 'success',
        data: rooms
      })
    }

    // Get all project rooms
    const rooms = await projectRoomsService.getProjectRooms()
    
    return NextResponse.json({
      status: 'success',
      data: rooms
    })
    
  } catch (error) {
    console.error('Project rooms API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch project rooms',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Basic validation
    if (!body.projectId || !body.name) {
      return NextResponse.json({
        status: 'error',
        message: 'Missing required fields: projectId, name'
      }, { status: 400 })
    }

    // Validate numeric fields if provided
    if (body.length !== undefined && (typeof body.length !== 'number' || body.length <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Length must be a positive number'
      }, { status: 400 })
    }

    if (body.width !== undefined && (typeof body.width !== 'number' || body.width <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Width must be a positive number'
      }, { status: 400 })
    }

    if (body.height !== undefined && (typeof body.height !== 'number' || body.height <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Height must be a positive number'
      }, { status: 400 })
    }

    const room = await projectRoomsService.createProjectRoom(body)
    
    if (!room) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to create project room'
      }, { status: 400 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: room
    }, { status: 201 })
    
  } catch (error) {
    console.error('Create project room API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to create project room',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'Project room ID is required'
      }, { status: 400 })
    }

    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid project room ID format'
      }, { status: 400 })
    }

    // Validate numeric fields if provided
    if (updateData.length !== undefined && (typeof updateData.length !== 'number' || updateData.length <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Length must be a positive number'
      }, { status: 400 })
    }

    if (updateData.width !== undefined && (typeof updateData.width !== 'number' || updateData.width <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Width must be a positive number'
      }, { status: 400 })
    }

    if (updateData.height !== undefined && (typeof updateData.height !== 'number' || updateData.height <= 0)) {
      return NextResponse.json({
        status: 'error',
        message: 'Height must be a positive number'
      }, { status: 400 })
    }

    const room = await projectRoomsService.updateProjectRoom(id, updateData)
    
    if (!room) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to update project room or project room not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: room
    })
    
  } catch (error) {
    console.error('Update project room API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to update project room',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'Project room ID is required'
      }, { status: 400 })
    }
    
    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid project room ID format'
      }, { status: 400 })
    }
    
    const success = await projectRoomsService.deleteProjectRoom(id)
    
    if (!success) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to delete project room'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      message: 'Project room deleted successfully'
    })
    
  } catch (error) {
    console.error('Delete project room API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to delete project room',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 