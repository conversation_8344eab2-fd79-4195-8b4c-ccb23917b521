'use client'

import React from 'react'
import { 
  User,
  Mail,
  Phone,
  MapPin,
  Heart,
  DollarSign,
  MessageSquare,
  Calendar,
  Tag,
  X
} from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useTheme } from '@/contexts/theme-context'
import type { Customer } from '@/types'

interface CustomerDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  customer: Customer | null
}

const windowTypeLabels: Record<string, string> = {
  'blinds': 'Blinds',
  'shutters': 'Shutters',
  'shades': 'Shades',
  'curtains': 'Curtains',
  'drapes': 'Drapes',
  'valances': 'Valances'
}

const budgetRangeLabels: Record<string, string> = {
  'under-500': 'Under $500',
  '500-1000': '$500 - $1,000',
  '1000-2500': '$1,000 - $2,500',
  '2500-5000': '$2,500 - $5,000',
  'over-5000': 'Over $5,000'
}

const communicationLabels: Record<string, string> = {
  'email': 'Email',
  'phone': 'Phone',
  'text': 'Text/SMS',
  'app': 'In-App Notifications'
}

const colorLabels: Record<string, string> = {
  'white': 'White',
  'cream': 'Cream',
  'beige': 'Beige',
  'gray': 'Gray',
  'brown': 'Brown',
  'black': 'Black'
}

export function CustomerDetailsModal({ isOpen, onClose, customer }: CustomerDetailsModalProps) {
  const { accentColor } = useTheme()

  if (!customer) return null

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl sm:max-w-4xl max-h-[80vh] overflow-hidden p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src="" />
                  <AvatarFallback className="text-lg font-semibold" style={{ backgroundColor: accentColor, color: 'white' }}>
                    {getInitials(customer.firstName, customer.lastName)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <DialogTitle className="text-xl font-semibold text-slate-700">
                    {customer.firstName} {customer.lastName}
                  </DialogTitle>
                  <p className="text-sm text-slate-500">Customer Details</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-6">
                {/* Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-slate-400" />
                      <div>
                        <p className="text-sm font-medium text-slate-600">Email</p>
                        <p className="text-slate-800">{customer.email}</p>
                      </div>
                    </div>
                    {customer.phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="h-4 w-4 text-slate-400" />
                        <div>
                          <p className="text-sm font-medium text-slate-600">Phone</p>
                          <p className="text-slate-800">{customer.phone}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Address Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      <p className="text-slate-800">{customer.address.street}</p>
                      <p className="text-slate-800">
                        {customer.address.city}, {customer.address.state} {customer.address.zipCode}
                      </p>
                      <p className="text-slate-600">{customer.address.country}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Account Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      Account Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-slate-600">Customer Since</p>
                      <p className="text-slate-800">{formatDate(customer.createdAt)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-slate-600">Last Updated</p>
                      <p className="text-slate-800">{formatDate(customer.updatedAt)}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                      <Heart className="h-5 w-5" />
                      Preferences
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Window Types */}
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-2">Window Treatment Types</p>
                      <div className="flex flex-wrap gap-2">
                        {customer.preferences.windowTreatmentTypes.length > 0 ? (
                          customer.preferences.windowTreatmentTypes.map((type) => (
                            <Badge key={type} variant="secondary" className="text-xs">
                              {windowTypeLabels[type] || type}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-slate-400 text-sm">No preferences set</p>
                        )}
                      </div>
                    </div>

                    {/* Preferred Colors */}
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-2">Preferred Colors</p>
                      <div className="flex flex-wrap gap-2">
                        {customer.preferences.preferredColors.length > 0 ? (
                          customer.preferences.preferredColors.map((color) => (
                            <Badge key={color} variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              {colorLabels[color] || color}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-slate-400 text-sm">No preferences set</p>
                        )}
                      </div>
                    </div>

                    {/* Budget Range */}
                    {customer.preferences.budget && (
                      <div>
                        <p className="text-sm font-medium text-slate-600 mb-2">Budget Range</p>
                        <Badge variant="default" className="text-xs" style={{ backgroundColor: accentColor }}>
                          <DollarSign className="h-3 w-3 mr-1" />
                          {budgetRangeLabels[customer.preferences.budget]}
                        </Badge>
                      </div>
                    )}

                    {/* Communication Preference */}
                    <div>
                      <p className="text-sm font-medium text-slate-600 mb-2">Communication Method</p>
                      <Badge variant="secondary" className="text-xs">
                        {communicationLabels[customer.preferences.communication]}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Notes */}
                {customer.notes && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                        <MessageSquare className="h-5 w-5" />
                        Notes
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-slate-700 whitespace-pre-wrap">{customer.notes}</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-end">
              <Button
                onClick={onClose}
                className="text-white"
                style={{ 
                  backgroundColor: accentColor,
                  borderColor: accentColor
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 