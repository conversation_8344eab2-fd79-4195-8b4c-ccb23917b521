'use client'

import React, { useState, useCallback } from 'react'
import { 
  User,
  Mail,
  Phone,
  MapPin,
  Heart,
  DollarSign,
  MessageSquare,
  Sparkles,
  Save,
  Loader2,
  AlertCircle
} from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useTheme } from '@/contexts/theme-context'

// Types
interface CustomerFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  preferences: {
    windowTypes: string[]
    preferredColors: string[]
    budgetRange: string
    communicationMethod: string
  }
  notes: string
}

interface NewCustomerModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (customer: CustomerFormData) => Promise<void>
}

const windowTypes = [
  { id: 'blinds', label: 'Blinds', popular: true },
  { id: 'shutters', label: 'Shutters', popular: true },
  { id: 'shades', label: 'Shades', popular: true },
  { id: 'curtains', label: 'Curtains', popular: false },
  { id: 'drapes', label: 'Drapes', popular: false },
  { id: 'valances', label: 'Valances', popular: false }
]

const colorOptions = [
  { id: 'white', label: 'White', hex: '#FFFFFF' },
  { id: 'cream', label: 'Cream', hex: '#F5F5DC' },
  { id: 'beige', label: 'Beige', hex: '#F5F5DC' },
  { id: 'gray', label: 'Gray', hex: '#808080' },
  { id: 'brown', label: 'Brown', hex: '#A52A2A' },
  { id: 'black', label: 'Black', hex: '#000000' }
]

const budgetRanges = [
  { value: 'under-500', label: 'Under $500' },
  { value: '500-1000', label: '$500 - $1,000' },
  { value: '1000-2500', label: '$1,000 - $2,500' },
  { value: '2500-5000', label: '$2,500 - $5,000' },
  { value: 'over-5000', label: 'Over $5,000' }
]

const communicationMethods = [
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'text', label: 'Text/SMS' },
  { value: 'app', label: 'In-App Notifications' }
]

export function NewCustomerModal({ isOpen, onClose, onSave }: NewCustomerModalProps) {
  const { accentColor } = useTheme()
  const [activeTab, setActiveTab] = useState('details')
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [aiSuggesting, setAiSuggesting] = useState(false)

  const [formData, setFormData] = useState<CustomerFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States'
    },
    preferences: {
      windowTypes: [],
      preferredColors: [],
      budgetRange: '',
      communicationMethod: 'email'
    },
    notes: ''
  })

  // Validation
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required'
    if (!formData.address.street.trim()) newErrors.street = 'Street address is required'
    if (!formData.address.city.trim()) newErrors.city = 'City is required'
    if (!formData.address.state.trim()) newErrors.state = 'State is required'
    if (!formData.address.zipCode.trim()) newErrors.zipCode = 'ZIP code is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])

  // AI Suggestion Simulation
  const suggestPreferences = useCallback(async () => {
    setAiSuggesting(true)
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock suggestions based on "trends"
    const suggestions = {
      windowTypes: ['blinds', 'shutters'],
      preferredColors: ['white', 'gray'],
      budgetRange: '1000-2500',
      communicationMethod: 'email'
    }

    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        ...suggestions
      }
    }))
    
    setAiSuggesting(false)
  }, [])

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      // Switch to the tab with errors
      if (errors.firstName || errors.lastName || errors.email || errors.phone) {
        setActiveTab('details')
      } else if (errors.street || errors.city || errors.state || errors.zipCode) {
        setActiveTab('address')
      }
      return
    }

    setIsLoading(true)
    try {
      await onSave(formData)
      onClose()
      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'United States'
        },
        preferences: {
          windowTypes: [],
          preferredColors: [],
          budgetRange: '',
          communicationMethod: 'email'
        },
        notes: ''
      })
    } catch (error) {
      console.error('Error saving customer:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Update form data
  const updateFormData = (path: string, value: string | string[]) => {
    console.log('updateFormData called:', path, value)
    setFormData(prev => {
      console.log('Previous formData:', prev)
      
      if (path.startsWith('preferences.')) {
        const prefKey = path.split('.')[1]
        const newData = {
          ...prev,
          preferences: {
            ...prev.preferences,
            [prefKey]: value
          }
        }
        console.log('New formData (preferences):', newData)
        return newData
      } else if (path.startsWith('address.')) {
        const addressKey = path.split('.')[1]
        return {
          ...prev,
          address: {
            ...prev.address,
            [addressKey]: value
          }
        }
      } else {
        return {
          ...prev,
          [path]: value
        }
      }
    })
    
    // Clear error for this field
    if (errors[path]) {
      setErrors(prev => ({ ...prev, [path]: '' }))
    }
  }

  // Handle window type selection
  const toggleWindowType = (typeId: string) => {
    console.log('Toggling window type:', typeId, 'Current:', formData.preferences.windowTypes)
    const current = formData.preferences.windowTypes
    const updated = current.includes(typeId)
      ? current.filter(t => t !== typeId)
      : [...current, typeId]
    console.log('Updated window types:', updated)
    updateFormData('preferences.windowTypes', updated)
  }

  // Handle color selection
  const toggleColor = (colorId: string) => {
    console.log('Toggling color:', colorId, 'Current:', formData.preferences.preferredColors)
    const current = formData.preferences.preferredColors
    const updated = current.includes(colorId)
      ? current.filter(c => c !== colorId)
      : [...current, colorId]
    console.log('Updated colors:', updated)
    updateFormData('preferences.preferredColors', updated)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl sm:max-w-6xl max-h-[70vh] overflow-hidden p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
            <DialogTitle className="text-xl font-semibold text-slate-700">
              Add New Customer
            </DialogTitle>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="details" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Details & Address
                </TabsTrigger>
                <TabsTrigger value="preferences" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Preferences & Notes
                </TabsTrigger>
              </TabsList>

              {/* Details & Address Tab */}
              <TabsContent value="details" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                        <User className="h-5 w-5" />
                        Personal Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName" className="text-slate-600">
                            First Name *
                          </Label>
                          <Input
                            id="firstName"
                            value={formData.firstName}
                            onChange={(e) => updateFormData('firstName', e.target.value)}
                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.firstName ? 'border-red-500' : ''
                            }`}
                            placeholder="Enter first name"
                          />
                          {errors.firstName && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.firstName}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName" className="text-slate-600">
                            Last Name *
                          </Label>
                          <Input
                            id="lastName"
                            value={formData.lastName}
                            onChange={(e) => updateFormData('lastName', e.target.value)}
                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.lastName ? 'border-red-500' : ''
                            }`}
                            placeholder="Enter last name"
                          />
                          {errors.lastName && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.lastName}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-slate-600">
                          Email Address *
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => updateFormData('email', e.target.value)}
                            className={`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.email ? 'border-red-500' : ''
                            }`}
                            placeholder="<EMAIL>"
                          />
                        </div>
                        {errors.email && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.email}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-slate-600">
                          Phone Number *
                        </Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                          <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => updateFormData('phone', e.target.value)}
                            className={`pl-10 border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.phone ? 'border-red-500' : ''
                            }`}
                            placeholder="(*************"
                          />
                        </div>
                        {errors.phone && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.phone}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Address Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Address Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="street" className="text-slate-600">
                          Street Address *
                        </Label>
                        <Input
                          id="street"
                          value={formData.address.street}
                          onChange={(e) => updateFormData('address.street', e.target.value)}
                          className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                            errors.street ? 'border-red-500' : ''
                          }`}
                          placeholder="123 Main Street"
                        />
                        {errors.street && (
                          <p className="text-sm text-red-600 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.street}
                          </p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="city" className="text-slate-600">
                            City *
                          </Label>
                          <Input
                            id="city"
                            value={formData.address.city}
                            onChange={(e) => updateFormData('address.city', e.target.value)}
                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.city ? 'border-red-500' : ''
                            }`}
                            placeholder="Springfield"
                          />
                          {errors.city && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.city}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="state" className="text-slate-600">
                            State *
                          </Label>
                          <Input
                            id="state"
                            value={formData.address.state}
                            onChange={(e) => updateFormData('address.state', e.target.value)}
                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.state ? 'border-red-500' : ''
                            }`}
                            placeholder="IL"
                          />
                          {errors.state && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.state}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="zipCode" className="text-slate-600">
                            ZIP Code *
                          </Label>
                          <Input
                            id="zipCode"
                            value={formData.address.zipCode}
                            onChange={(e) => updateFormData('address.zipCode', e.target.value)}
                            className={`border-gray-200 focus:border-[var(--color-brand-primary)] ${
                              errors.zipCode ? 'border-red-500' : ''
                            }`}
                            placeholder="62704"
                          />
                          {errors.zipCode && (
                            <p className="text-sm text-red-600 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.zipCode}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="country" className="text-slate-600">
                            Country
                          </Label>
                          <Select 
                            value={formData.address.country} 
                            onValueChange={(value) => updateFormData('address.country', value)}
                          >
                            <SelectTrigger className="border-gray-200">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="United States">United States</SelectItem>
                              <SelectItem value="Canada">Canada</SelectItem>
                              <SelectItem value="Mexico">Mexico</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Preferences & Notes Tab */}
              <TabsContent value="preferences" className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-700">Customer Preferences & Notes</h3>
                  <Button
                    variant="outline"
                    onClick={suggestPreferences}
                    disabled={aiSuggesting}
                    className="border-gray-200"
                    style={{ borderColor: accentColor, color: accentColor }}
                  >
                    {aiSuggesting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4 mr-2" />
                    )}
                    {aiSuggesting ? 'Analyzing...' : 'AI Suggest'}
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left Column - Preferences */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base text-slate-700">Window Types</CardTitle>
                        <p className="text-sm text-slate-500">Select preferred window treatment types</p>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 gap-3">
                          {windowTypes.map((type) => (
                            <div key={type.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={type.id}
                                checked={formData.preferences.windowTypes.includes(type.id)}
                                onCheckedChange={(checked) => {
                                  console.log('Checkbox changed:', type.id, checked)
                                  toggleWindowType(type.id)
                                }}
                                className="border-gray-300"
                              />
                              <Label htmlFor={type.id} className="text-sm font-normal text-slate-600 flex items-center gap-2">
                                {type.label}
                                {type.popular && (
                                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                                    Popular
                                  </Badge>
                                )}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base text-slate-700">Preferred Colors</CardTitle>
                        <p className="text-sm text-slate-500">Select preferred color schemes</p>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-3">
                          {colorOptions.map((color) => (
                            <div key={color.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={color.id}
                                checked={formData.preferences.preferredColors.includes(color.id)}
                                onCheckedChange={(checked) => {
                                  console.log('Color checkbox changed:', color.id, checked)
                                  toggleColor(color.id)
                                }}
                                className="border-gray-300"
                              />
                              <Label htmlFor={color.id} className="text-sm font-normal text-slate-600 flex items-center gap-2">
                                <div 
                                  className="w-3 h-3 rounded-full border border-gray-300"
                                  style={{ backgroundColor: color.hex }}
                                />
                                {color.label}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Right Column - Budget, Communication & Notes */}
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base text-slate-700 flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          Budget Range
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Select 
                          value={formData.preferences.budgetRange} 
                          onValueChange={(value) => updateFormData('preferences.budgetRange', value)}
                        >
                          <SelectTrigger className="border-gray-200">
                            <SelectValue placeholder="Select budget range" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetRanges.map((range) => (
                              <SelectItem key={range.value} value={range.value}>
                                {range.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base text-slate-700">Communication</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Select 
                          value={formData.preferences.communicationMethod} 
                          onValueChange={(value) => updateFormData('preferences.communicationMethod', value)}
                        >
                          <SelectTrigger className="border-gray-200">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {communicationMethods.map((method) => (
                              <SelectItem key={method.value} value={method.value}>
                                {method.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg text-slate-700 flex items-center gap-2">
                          <MessageSquare className="h-5 w-5" />
                          Additional Notes
                        </CardTitle>
                        <p className="text-sm text-slate-500">
                          Add any additional information about the customer
                        </p>
                      </CardHeader>
                      <CardContent>
                        <Textarea
                          value={formData.notes}
                          onChange={(e) => updateFormData('notes', e.target.value)}
                          className="min-h-[200px] border-gray-200 focus:border-[var(--color-brand-primary)]"
                          placeholder="Special requirements, previous interactions, design preferences, installation considerations..."
                        />
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="border-gray-200"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
                className="text-white min-w-[120px]"
                style={{ 
                  backgroundColor: accentColor,
                  borderColor: accentColor
                }}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Customer
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

