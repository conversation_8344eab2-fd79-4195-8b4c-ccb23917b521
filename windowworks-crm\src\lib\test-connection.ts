import { supabase, getServiceClient } from './supabase/client'

// Test browser client connection
export async function testBrowserConnection() {
  try {
    const { data, error } = await supabase.from('users').select('count(*)')
    
    if (error) {
      console.error('Browser client error:', error)
      return false
    }
    
    console.log('Browser client connected successfully:', data)
    return true
  } catch (error) {
    console.error('Browser client connection failed:', error)
    return false
  }
}

// Test service client connection
export async function testServiceConnection() {
  try {
    const serviceClient = getServiceClient()
    const { data, error } = await serviceClient.from('users').select('count(*)')
    
    if (error) {
      console.error('Service client error:', error)
      return false
    }
    
    console.log('Service client connected successfully:', data)
    return true
  } catch (error) {
    console.error('Service client connection failed:', error)
    return false
  }
}
