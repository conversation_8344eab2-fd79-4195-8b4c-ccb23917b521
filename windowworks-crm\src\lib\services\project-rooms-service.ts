import { supabase } from '@/lib/supabase/client'

export interface ProjectRoom {
  id: string
  projectId: string
  name: string
  length?: number
  width?: number
  height?: number
  measurementNotes?: string
  notes?: string
  createdAt: string
  updatedAt: string
  // Related data
  project?: {
    id: string
    title: string
    description: string
  }
  windowSpecifications?: WindowSpecification[]
}

export interface WindowSpecification {
  id: string
  roomId: string
  width: number
  height: number
  treatmentType: string
  color: string
  material: string
  installationType: string
  features: string[]
  cost: number
  createdAt: string
  updatedAt: string
}

export class ProjectRoomsService {
  private supabase = supabase

  async getProjectRooms(): Promise<ProjectRoom[]> {
    try {
      console.log('Fetching project rooms from database...')
      
      const { data, error } = await this.supabase
        .from('project_rooms')
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          ),
          window_specifications (
            id,
            room_id,
            width,
            height,
            treatment_type,
            color,
            material,
            installation_type,
            features,
            cost,
            created_at,
            updated_at
          )
        `)
        .order('created_at', { ascending: false })

      console.log('Project rooms query response:', { data, error })

      if (error) {
        console.error('Failed to fetch project rooms:', error)
        return []
      }

      const mappedRooms = (data || []).map((room) => ({
        id: room.id,
        projectId: room.project_id,
        name: room.name,
        length: room.length || undefined,
        width: room.width || undefined,
        height: room.height || undefined,
        measurementNotes: room.measurement_notes || undefined,
        notes: room.notes || undefined,
        createdAt: room.created_at,
        updatedAt: room.updated_at,
        project: room.projects ? {
          id: room.projects.id,
          title: room.projects.title,
          description: room.projects.description
        } : undefined,
        windowSpecifications: room.window_specifications?.map((spec) => ({
          id: spec.id,
          roomId: spec.room_id,
          width: spec.width,
          height: spec.height,
          treatmentType: spec.treatment_type,
          color: spec.color,
          material: spec.material,
          installationType: spec.installation_type,
          features: (spec.features as string[]) || [],
          cost: spec.cost,
          createdAt: spec.created_at,
          updatedAt: spec.updated_at
        })) || []
      }))

      console.log('Mapped project rooms:', mappedRooms)
      return mappedRooms
    } catch (error) {
      console.error('Project rooms service error:', error)
      return []
    }
  }

  async getProjectRoomById(id: string): Promise<ProjectRoom | null> {
    try {
      const { data, error } = await this.supabase
        .from('project_rooms')
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          ),
          window_specifications (
            id,
            room_id,
            width,
            height,
            treatment_type,
            color,
            material,
            installation_type,
            features,
            cost,
            created_at,
            updated_at
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        console.error('Failed to fetch project room:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        name: data.name,
        length: data.length || undefined,
        width: data.width || undefined,
        height: data.height || undefined,
        measurementNotes: data.measurement_notes || undefined,
        notes: data.notes || undefined,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        windowSpecifications: data.window_specifications?.map((spec) => ({
          id: spec.id,
          roomId: spec.room_id,
          width: spec.width,
          height: spec.height,
          treatmentType: spec.treatment_type,
          color: spec.color,
          material: spec.material,
          installationType: spec.installation_type,
          features: (spec.features as string[]) || [],
          cost: spec.cost,
          createdAt: spec.created_at,
          updatedAt: spec.updated_at
        })) || []
      }
    } catch (error) {
      console.error('Get project room by ID service error:', error)
      return null
    }
  }

  async getProjectRoomsByProject(projectId: string): Promise<ProjectRoom[]> {
    try {
      const { data, error } = await this.supabase
        .from('project_rooms')
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          ),
          window_specifications (
            id,
            room_id,
            width,
            height,
            treatment_type,
            color,
            material,
            installation_type,
            features,
            cost,
            created_at,
            updated_at
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to fetch project rooms by project:', error)
        return []
      }

      return (data || []).map((room) => ({
        id: room.id,
        projectId: room.project_id,
        name: room.name,
        length: room.length || undefined,
        width: room.width || undefined,
        height: room.height || undefined,
        measurementNotes: room.measurement_notes || undefined,
        notes: room.notes || undefined,
        createdAt: room.created_at,
        updatedAt: room.updated_at,
        project: room.projects ? {
          id: room.projects.id,
          title: room.projects.title,
          description: room.projects.description
        } : undefined,
        windowSpecifications: room.window_specifications?.map((spec) => ({
          id: spec.id,
          roomId: spec.room_id,
          width: spec.width,
          height: spec.height,
          treatmentType: spec.treatment_type,
          color: spec.color,
          material: spec.material,
          installationType: spec.installation_type,
          features: (spec.features as string[]) || [],
          cost: spec.cost,
          createdAt: spec.created_at,
          updatedAt: spec.updated_at
        })) || []
      }))
    } catch (error) {
      console.error('Get project rooms by project service error:', error)
      return []
    }
  }

  async createProjectRoom(roomData: Omit<ProjectRoom, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProjectRoom | null> {
    try {
      const { data, error } = await this.supabase
        .from('project_rooms')
        .insert({
          project_id: roomData.projectId,
          name: roomData.name,
          length: roomData.length,
          width: roomData.width,
          height: roomData.height,
          measurement_notes: roomData.measurementNotes,
          notes: roomData.notes
        })
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          )
        `)
        .single()

      if (error) {
        console.error('Failed to create project room:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        name: data.name,
        length: data.length || undefined,
        width: data.width || undefined,
        height: data.height || undefined,
        measurementNotes: data.measurement_notes || undefined,
        notes: data.notes || undefined,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        windowSpecifications: []
      }
    } catch (error) {
      console.error('Create project room service error:', error)
      return null
    }
  }

  async updateProjectRoom(id: string, roomData: Partial<ProjectRoom>): Promise<ProjectRoom | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (roomData.projectId !== undefined) updateData.project_id = roomData.projectId
      if (roomData.name !== undefined) updateData.name = roomData.name
      if (roomData.length !== undefined) updateData.length = roomData.length
      if (roomData.width !== undefined) updateData.width = roomData.width
      if (roomData.height !== undefined) updateData.height = roomData.height
      if (roomData.measurementNotes !== undefined) updateData.measurement_notes = roomData.measurementNotes
      if (roomData.notes !== undefined) updateData.notes = roomData.notes

      const { data, error } = await this.supabase
        .from('project_rooms')
        .update(updateData)
        .eq('id', id)
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          ),
          window_specifications (
            id,
            room_id,
            width,
            height,
            treatment_type,
            color,
            material,
            installation_type,
            features,
            cost,
            created_at,
            updated_at
          )
        `)
        .single()

      if (error) {
        console.error('Failed to update project room:', error)
        return null
      }

      return {
        id: data.id,
        projectId: data.project_id,
        name: data.name,
        length: data.length || undefined,
        width: data.width || undefined,
        height: data.height || undefined,
        measurementNotes: data.measurement_notes || undefined,
        notes: data.notes || undefined,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        project: data.projects ? {
          id: data.projects.id,
          title: data.projects.title,
          description: data.projects.description
        } : undefined,
        windowSpecifications: data.window_specifications?.map((spec) => ({
          id: spec.id,
          roomId: spec.room_id,
          width: spec.width,
          height: spec.height,
          treatmentType: spec.treatment_type,
          color: spec.color,
          material: spec.material,
          installationType: spec.installation_type,
          features: (spec.features as string[]) || [],
          cost: spec.cost,
          createdAt: spec.created_at,
          updatedAt: spec.updated_at
        })) || []
      }
    } catch (error) {
      console.error('Update project room service error:', error)
      return null
    }
  }

  async deleteProjectRoom(id: string): Promise<boolean> {
    try {
      // First delete all window specifications for this room
      const { error: windowsError } = await this.supabase
        .from('window_specifications')
        .delete()
        .eq('room_id', id)

      if (windowsError) {
        console.error('Failed to delete window specifications:', windowsError)
        return false
      }

      // Then delete the room
      const { error } = await this.supabase
        .from('project_rooms')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete project room:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete project room service error:', error)
      return false
    }
  }

  async getProjectRoomStats() {
    try {
      // Get total project rooms count
      const { count: totalRooms, error: totalError } = await this.supabase
        .from('project_rooms')
        .select('*', { count: 'exact', head: true })

      if (totalError) {
        console.error('Failed to get total project rooms count:', totalError)
        return {
          totalRooms: 0,
          roomsByProject: 0,
          averageRoomSize: 0,
          totalWindowSpecifications: 0
        }
      }

      // Get average room size
      const { data: sizeData, error: sizeError } = await this.supabase
        .from('project_rooms')
        .select('length, width, height')
        .not('length', 'is', null)
        .not('width', 'is', null)
        .not('height', 'is', null)

      let averageRoomSize = 0
      if (!sizeError && sizeData && sizeData.length > 0) {
        const totalVolume = sizeData.reduce((sum, room) => {
          const length = room.length || 0
          const width = room.width || 0
          const height = room.height || 0
          return sum + (length * width * height)
        }, 0)
        averageRoomSize = Math.round(totalVolume / sizeData.length)
      }

      // Get total window specifications count
      const { count: totalWindowSpecs, error: windowsError } = await this.supabase
        .from('window_specifications')
        .select('*', { count: 'exact', head: true })

      return {
        totalRooms: totalRooms || 0,
        roomsByProject: totalRooms || 0, // This could be more sophisticated
        averageRoomSize,
        totalWindowSpecifications: totalWindowSpecs || 0
      }
    } catch (error) {
      console.error('Get project room stats service error:', error)
      return {
        totalRooms: 0,
        roomsByProject: 0,
        averageRoomSize: 0,
        totalWindowSpecifications: 0
      }
    }
  }

  async searchProjectRooms(query: string): Promise<ProjectRoom[]> {
    try {
      const { data, error } = await this.supabase
        .from('project_rooms')
        .select(`
          id,
          project_id,
          name,
          length,
          width,
          height,
          measurement_notes,
          notes,
          created_at,
          updated_at,
          projects (
            id,
            title,
            description
          ),
          window_specifications (
            id,
            room_id,
            width,
            height,
            treatment_type,
            color,
            material,
            installation_type,
            features,
            cost,
            created_at,
            updated_at
          )
        `)
        .or(`name.ilike.%${query}%,notes.ilike.%${query}%,measurement_notes.ilike.%${query}%`)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to search project rooms:', error)
        return []
      }

      return (data || []).map((room) => ({
        id: room.id,
        projectId: room.project_id,
        name: room.name,
        length: room.length || undefined,
        width: room.width || undefined,
        height: room.height || undefined,
        measurementNotes: room.measurement_notes || undefined,
        notes: room.notes || undefined,
        createdAt: room.created_at,
        updatedAt: room.updated_at,
        project: room.projects ? {
          id: room.projects.id,
          title: room.projects.title,
          description: room.projects.description
        } : undefined,
        windowSpecifications: room.window_specifications?.map((spec) => ({
          id: spec.id,
          roomId: spec.room_id,
          width: spec.width,
          height: spec.height,
          treatmentType: spec.treatment_type,
          color: spec.color,
          material: spec.material,
          installationType: spec.installation_type,
          features: (spec.features as string[]) || [],
          cost: spec.cost,
          createdAt: spec.created_at,
          updatedAt: spec.updated_at
        })) || []
      }))
    } catch (error) {
      console.error('Search project rooms service error:', error)
      return []
    }
  }
}

export const projectRoomsService = new ProjectRoomsService() 