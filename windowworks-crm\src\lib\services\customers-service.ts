import { supabase } from '@/lib/supabase/client'
import type { Customer, WindowTreatmentType, BudgetRange, CommunicationPreference } from '@/types'

export class CustomersService {
  private supabase = supabase

  async getCustomers(): Promise<Customer[]> {
    try {
      console.log('Fetching customers from database...')
      
      const { data, error } = await this.supabase
        .from('customers')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone,
          address_street,
          address_city,
          address_state,
          address_zip_code,
          address_country,
          preferences,
          notes,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false })

      console.log('Customers query response:', { data, error })

      if (error) {
        console.error('Failed to fetch customers:', error)
        return []
      }

      const mappedCustomers = (data || []).map((customer) => ({
        id: customer.id,
        firstName: customer.first_name,
        lastName: customer.last_name,
        email: customer.email,
        phone: customer.phone || undefined,
        address: {
          street: customer.address_street,
          city: customer.address_city,
          state: customer.address_state,
          zipCode: customer.address_zip_code,
          country: customer.address_country || 'US'
        },
        preferences: {
          windowTreatmentTypes: (customer.preferences as Record<string, unknown>)?.windowTreatmentTypes as WindowTreatmentType[] || [],
          preferredColors: (customer.preferences as Record<string, unknown>)?.preferredColors as string[] || [],
          budget: (customer.preferences as Record<string, unknown>)?.budget as BudgetRange,
          communication: (customer.preferences as Record<string, unknown>)?.communication as CommunicationPreference || 'email'
        },
        notes: customer.notes || undefined,
        createdAt: customer.created_at,
        updatedAt: customer.updated_at,
        createdBy: '' // This would need to be fetched separately if needed
      }))

      console.log('Mapped customers:', mappedCustomers)
      return mappedCustomers
    } catch (error) {
      console.error('Customers service error:', error)
      return []
    }
  }

  async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>): Promise<Customer | null> {
    try {
      const { data, error } = await this.supabase
        .from('customers')
        .insert({
          first_name: customerData.firstName,
          last_name: customerData.lastName,
          email: customerData.email,
          phone: customerData.phone,
          address_street: customerData.address.street,
          address_city: customerData.address.city,
          address_state: customerData.address.state,
          address_zip_code: customerData.address.zipCode,
          address_country: customerData.address.country,
          preferences: JSON.stringify(customerData.preferences),
          notes: customerData.notes
        })
        .select()
        .single()

      if (error) {
        console.error('Failed to create customer:', error)
        return null
      }

      return {
        id: data.id,
        firstName: data.first_name,
        lastName: data.last_name,
        email: data.email,
        phone: data.phone || undefined,
        address: {
          street: data.address_street,
          city: data.address_city,
          state: data.address_state,
          zipCode: data.address_zip_code,
          country: data.address_country || 'US'
        },
        preferences: data.preferences ? JSON.parse(data.preferences as string) : {},
        notes: data.notes || undefined,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: '' // This would need to be set based on current user
      }
    } catch (error) {
      console.error('Create customer service error:', error)
      return null
    }
  }

  async updateCustomer(id: string, customerData: Partial<Customer>): Promise<Customer | null> {
    try {
      const updateData: Record<string, unknown> = {}
      
      if (customerData.firstName) updateData.first_name = customerData.firstName
      if (customerData.lastName) updateData.last_name = customerData.lastName
      if (customerData.email) updateData.email = customerData.email
      if (customerData.phone !== undefined) updateData.phone = customerData.phone
      if (customerData.address) {
        updateData.address_street = customerData.address.street
        updateData.address_city = customerData.address.city
        updateData.address_state = customerData.address.state
        updateData.address_zip_code = customerData.address.zipCode
        updateData.address_country = customerData.address.country
      }
      if (customerData.preferences) updateData.preferences = JSON.stringify(customerData.preferences)
      if (customerData.notes !== undefined) updateData.notes = customerData.notes

      const { data, error } = await this.supabase
        .from('customers')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Failed to update customer:', error)
        return null
      }

      return {
        id: data.id,
        firstName: data.first_name,
        lastName: data.last_name,
        email: data.email,
        phone: data.phone || undefined,
        address: {
          street: data.address_street,
          city: data.address_city,
          state: data.address_state,
          zipCode: data.address_zip_code,
          country: data.address_country || 'US'
        },
        preferences: data.preferences ? JSON.parse(data.preferences as string) : {},
        notes: data.notes || undefined,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        createdBy: ''
      }
    } catch (error) {
      console.error('Update customer service error:', error)
      return null
    }
  }

  async getCustomerProjectCount(customerId: string): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', customerId)

      if (error) {
        console.error('Failed to get customer project count:', error)
        return 0
      }

      return count || 0
    } catch (error) {
      console.error('Get customer project count service error:', error)
      return 0
    }
  }

  async getCustomerAppointmentCount(customerId: string): Promise<number> {
    try {
      // First, get all project IDs for this customer
      const { data: projects, error: projectsError } = await this.supabase
        .from('projects')
        .select('id')
        .eq('customer_id', customerId)

      if (projectsError) {
        console.error('Failed to get customer projects:', projectsError)
        return 0
      }

      if (!projects || projects.length === 0) {
        return 0
      }

      const projectIds = projects.map(p => p.id)

      // Then count installation tasks for these projects
      const { count, error } = await this.supabase
        .from('installation_tasks')
        .select('*', { count: 'exact', head: true })
        .in('project_id', projectIds)

      if (error) {
        console.error('Failed to get customer appointment count:', error)
        return 0
      }

      return count || 0
    } catch (error) {
      console.error('Get customer appointment count service error:', error)
      return 0
    }
  }

  async deleteCustomer(id: string): Promise<boolean> {
    try {
      // First, get all project IDs for this customer
      const { data: projects, error: projectsError } = await this.supabase
        .from('projects')
        .select('id')
        .eq('customer_id', id)

      if (projectsError) {
        console.error('Failed to get customer projects:', projectsError)
        return false
      }

      // Delete related appointments/installation_tasks for all customer projects
      if (projects && projects.length > 0) {
        const projectIds = projects.map(p => p.id)
        
        const { error: appointmentsError } = await this.supabase
          .from('installation_tasks')
          .delete()
          .in('project_id', projectIds)

        if (appointmentsError) {
          console.error('Failed to delete customer appointments:', appointmentsError)
          return false
        }

        // Also delete the projects
        const { error: projectsDeleteError } = await this.supabase
          .from('projects')
          .delete()
          .eq('customer_id', id)

        if (projectsDeleteError) {
          console.error('Failed to delete customer projects:', projectsDeleteError)
          return false
        }
      }

      // Finally delete the customer
      const { error } = await this.supabase
        .from('customers')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Failed to delete customer:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Delete customer service error:', error)
      return false
    }
  }
}

export const customersService = new CustomersService()
