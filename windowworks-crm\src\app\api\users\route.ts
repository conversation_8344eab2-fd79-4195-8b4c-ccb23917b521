import { NextRequest, NextResponse } from 'next/server'
import { usersService } from '@/lib/services/users-service'

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')
    const id = url.searchParams.get('id')

    // Get single user by ID
    if (id) {
      const user = await usersService.getUserById(id)
      if (!user) {
        return NextResponse.json({
          status: 'error',
          message: 'User not found'
        }, { status: 404 })
      }
      return NextResponse.json({
        status: 'success',
        data: user
      })
    }

    // Search users
    if (search) {
      const users = await usersService.searchUsers(search)
      return NextResponse.json({
        status: 'success',
        data: users
      })
    }

    // Get users by role
    if (role) {
      const users = await usersService.getUsersByRole(role as any)
      return NextResponse.json({
        status: 'success',
        data: users
      })
    }

    // Get all users
    const users = await usersService.getUsers()
    
    return NextResponse.json({
      status: 'success',
      data: users
    })
    
  } catch (error) {
    console.error('Users API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch users',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Basic validation
    if (!body.email || !body.role || !body.profile?.firstName || !body.profile?.lastName) {
      return NextResponse.json({
        status: 'error',
        message: 'Missing required fields: email, role, profile.firstName, profile.lastName'
      }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid email format'
      }, { status: 400 })
    }

    // Validate role
    const validRoles = ['admin', 'installer', 'customer']
    if (!validRoles.includes(body.role)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid role. Must be one of: admin, installer, customer'
      }, { status: 400 })
    }

    const user = await usersService.createUser(body)
    
    if (!user) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to create user'
      }, { status: 400 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: user
    }, { status: 201 })
    
  } catch (error) {
    console.error('Create user API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to create user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'User ID is required'
      }, { status: 400 })
    }

    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid user ID format'
      }, { status: 400 })
    }

    // Validate email format if provided
    if (updateData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid email format'
      }, { status: 400 })
    }

    // Validate role if provided
    if (updateData.role && !['admin', 'installer', 'customer'].includes(updateData.role)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid role. Must be one of: admin, installer, customer'
      }, { status: 400 })
    }

    const user = await usersService.updateUser(id, updateData)
    
    if (!user) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to update user or user not found'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      data: user
    })
    
  } catch (error) {
    console.error('Update user API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to update user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get('id')
    
    if (!id) {
      return NextResponse.json({
        status: 'error',
        message: 'User ID is required'
      }, { status: 400 })
    }
    
    // Validate ID format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
      return NextResponse.json({
        status: 'error',
        message: 'Invalid user ID format'
      }, { status: 400 })
    }
    
    const success = await usersService.deleteUser(id)
    
    if (!success) {
      return NextResponse.json({
        status: 'error',
        message: 'Failed to delete user'
      }, { status: 404 })
    }
    
    return NextResponse.json({
      status: 'success',
      message: 'User deleted successfully'
    })
    
  } catch (error) {
    console.error('Delete user API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to delete user',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 