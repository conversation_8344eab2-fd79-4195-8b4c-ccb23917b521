import { z, ZodIssue } from 'zod'

// Customer validation schemas
export const CustomerCreateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(100, 'Last name too long'),
  email: z.string().email('Invalid email format').max(255, 'Email too long'),
  phone: z.string().optional(),
  address: z.object({
    street: z.string().min(1, 'Street address is required').max(255, 'Street address too long'),
    city: z.string().min(1, 'City is required').max(100, 'City name too long'),
    state: z.string().min(1, 'State is required').max(50, 'State name too long'),
    zipCode: z.string().min(1, 'ZIP code is required').max(20, 'ZIP code too long'),
    country: z.string().max(50, 'Country name too long').default('USA')
  }),
  preferences: z.object({
    windowTreatmentTypes: z.array(z.enum(['blinds', 'shutters', 'shades', 'curtains', 'drapes', 'valances'])).optional().default([]),
    preferredColors: z.array(z.string()).optional().default([]),
    budget: z.enum(['under-500', '500-1000', '1000-2500', '2500-5000', 'over-5000']).optional(),
    communication: z.enum(['email', 'phone', 'text', 'app']).default('email')
  }).optional().default(() => ({
    windowTreatmentTypes: [],
    preferredColors: [],
    communication: 'email' as const
  })),
  notes: z.string().optional()
})

export const CustomerUpdateSchema = CustomerCreateSchema.partial()

// Project validation schemas
export const ProjectCreateSchema = z.object({
  title: z.string().min(1, 'Project title is required').max(255, 'Title too long'),
  description: z.string().min(1, 'Project description is required').max(2000, 'Description too long'),
  customerId: z.string().uuid('Invalid customer ID'),
  estimatedCost: z.number().min(0, 'Cost cannot be negative').optional(),
  startDate: z.string().optional(),
  installerId: z.string().uuid('Invalid installer ID').optional()
})

export const ProjectUpdateSchema = z.object({
  title: z.string().min(1, 'Project title is required').max(255, 'Title too long').optional(),
  description: z.string().min(1, 'Project description is required').max(2000, 'Description too long').optional(),
  status: z.enum(['draft', 'quoted', 'approved', 'scheduled', 'in-progress', 'completed', 'cancelled', 'on-hold']).optional(),
  estimatedCost: z.number().min(0, 'Cost cannot be negative').optional(),
  actualCost: z.number().min(0, 'Cost cannot be negative').optional(),
  startDate: z.string().optional(),
  completionDate: z.string().optional(),
  installerId: z.string().uuid('Invalid installer ID').optional()
})

// Installation Task (Schedule) validation schemas
export const AppointmentCreateSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  startTime: z.string().refine((val) => !isNaN(Date.parse(val)), 'Invalid start time'),
  endTime: z.string().refine((val) => !isNaN(Date.parse(val)), 'Invalid end time').optional(),
  duration: z.number().min(15, 'Duration must be at least 15 minutes').max(1440, 'Duration cannot exceed 24 hours').optional().default(120),
  projectId: z.string().uuid('Invalid project ID'),
  installerId: z.string().uuid('Invalid installer ID').optional(),
  locationStreet: z.string().min(1, 'Street address is required').max(255, 'Street address too long'),
  locationCity: z.string().min(1, 'City is required').max(100, 'City name too long'),
  locationState: z.string().min(1, 'State is required').max(50, 'State name too long'),
  notes: z.string().max(2000, 'Notes too long').optional()
})

export const AppointmentUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long').optional(),
  startTime: z.string().refine((val) => !isNaN(Date.parse(val)), 'Invalid start time').optional(),
  endTime: z.string().refine((val) => !isNaN(Date.parse(val)), 'Invalid end time').optional(),
  duration: z.number().min(15, 'Duration must be at least 15 minutes').max(1440, 'Duration cannot exceed 24 hours').optional(),
  status: z.enum(['pending', 'in-progress', 'completed', 'cancelled', 'rescheduled']).optional(),
  installerId: z.string().uuid('Invalid installer ID').optional(),
  locationStreet: z.string().min(1, 'Street address is required').max(255, 'Street address too long').optional(),
  locationCity: z.string().min(1, 'City is required').max(100, 'City name too long').optional(),
  locationState: z.string().min(1, 'State is required').max(50, 'State name too long').optional(),
  notes: z.string().max(2000, 'Notes too long').optional(),
  actualDuration: z.number().min(1, 'Actual duration must be positive').optional()
})

// Generic validation result type
export interface ValidationResult<T> {
  success: boolean
  data?: T
  errors?: string[]
}

// Validation helper functions
export function validateCustomerCreate(data: unknown): ValidationResult<z.infer<typeof CustomerCreateSchema>> {
  try {
    const result = CustomerCreateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

export function validateCustomerUpdate(data: unknown): ValidationResult<z.infer<typeof CustomerUpdateSchema>> {
  try {
    const result = CustomerUpdateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

export function validateProjectCreate(data: unknown): ValidationResult<z.infer<typeof ProjectCreateSchema>> {
  try {
    const result = ProjectCreateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

export function validateProjectUpdate(data: unknown): ValidationResult<z.infer<typeof ProjectUpdateSchema>> {
  try {
    const result = ProjectUpdateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

export function validateAppointmentCreate(data: unknown): ValidationResult<z.infer<typeof AppointmentCreateSchema>> {
  try {
    const result = AppointmentCreateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

export function validateAppointmentUpdate(data: unknown): ValidationResult<z.infer<typeof AppointmentUpdateSchema>> {
  try {
    const result = AppointmentUpdateSchema.parse(data)
    return { success: true, data: result }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.issues.map((issue: ZodIssue) => `${issue.path.join('.')}: ${issue.message}`)
      }
    }
    return { success: false, errors: ['Invalid data format'] }
  }
}

// Business logic validation functions
export async function validateCustomerExists(customerId: string): Promise<boolean> {
  // This would typically check with the database
  // For now, return true if it's a valid UUID format
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(customerId)
}

export async function validateProjectExists(projectId: string): Promise<boolean> {
  // This would typically check with the database
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(projectId)
}

export async function validateInstallerExists(installerId: string): Promise<boolean> {
  // This would typically check with the database
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(installerId)
}

export function validateTimeRange(startTime: string, endTime?: string): boolean {
  if (!endTime) return true
  
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  return start < end
}

export function validateBusinessHours(startTime: string): boolean {
  const date = new Date(startTime)
  const hour = date.getHours()
  const day = date.getDay()
  
  // Monday to Friday, 8 AM to 6 PM
  return day >= 1 && day <= 5 && hour >= 8 && hour < 18
}
