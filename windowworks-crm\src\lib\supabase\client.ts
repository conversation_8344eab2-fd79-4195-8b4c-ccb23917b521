import { createServerClient } from '@supabase/ssr'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import type { Database } from './types'

// Singleton instance for browser client
let browserClientInstance: ReturnType<typeof createServerClient<Database>> | null = null

// Singleton instance for service client
let serviceClientInstance: ReturnType<typeof createSupabaseClient<Database>> | null = null

// Client-side Supabase client for browser (singleton)
export function createBrowserClient() {
  if (browserClientInstance) {
    return browserClientInstance
  }

  // Check if we have the required environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('Missing required Supabase environment variables: NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY')
  }

  browserClientInstance = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get: (name: string) => {
          if (typeof window !== 'undefined') {
            return document.cookie
              .split('; ')
              .find((row) => row.startsWith(`${name}=`))
              ?.split('=')[1]
          }
        },
        set: (name: string, value: string, options?: { maxAge?: number }) => {
          if (typeof window !== 'undefined') {
            document.cookie = `${name}=${value}; path=/; ${
              options?.maxAge ? `max-age=${options.maxAge}` : ''
            }`
          }
        },
        remove: (name: string) => {
          if (typeof window !== 'undefined') {
            document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`
          }
        },
      },
    }
  )

  return browserClientInstance
}

// Server-side Supabase client with service role key for admin operations (singleton)
export function createServiceClient() {
  if (serviceClientInstance) {
    return serviceClientInstance
  }

  // Only create service client if we have the service role key (server-side only)
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for service client')
  }

  serviceClientInstance = createSupabaseClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )

  return serviceClientInstance
}

// Legacy export for compatibility
export function createClient() {
  return createBrowserClient()
}

// Function to reset instances (useful for testing or cleanup)
export function resetClientInstances() {
  browserClientInstance = null
  serviceClientInstance = null
}

// Lazy getter for browser client (only create when accessed)
export function getBrowserClient() {
  return createBrowserClient()
}

// Lazy getter for service client (only create when accessed)
export function getServiceClient() {
  return createServiceClient()
}

// For backward compatibility, create a getter that returns the browser client
// This will only be called when actually accessed, not at module initialization
let _supabase: ReturnType<typeof createBrowserClient> | null = null
export const supabase = new Proxy({} as ReturnType<typeof createBrowserClient>, {
  get(target, prop) {
    if (!_supabase) {
      _supabase = createBrowserClient()
    }
    return (_supabase as any)[prop]
  }
})
