import { useState, useEffect } from 'react'
import { customersService } from '@/lib/services/customers-service'
import type { Customer } from '@/types'

interface UseCustomersDataResult {
  customers: Customer[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>) => Promise<Customer | null>
  updateCustomer: (id: string, customer: Partial<Customer>) => Promise<Customer | null>
  deleteCustomer: (id: string) => Promise<boolean>
  getCustomerAppointmentCount: (customerId: string) => Promise<number>
  getCustomerProjectCount: (customerId: string) => Promise<number>
}

export function useCustomersData(): UseCustomersDataResult {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCustomers = async () => {
    try {
      console.log('Hook: Starting to fetch customers...')
      setLoading(true)
      setError(null)
      const data = await customersService.getCustomers()
      console.log('Hook: Received customers data:', data)
      setCustomers(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch customers'
      console.error('Hook: Customers data fetch error:', err)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const createCustomer = async (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>): Promise<Customer | null> => {
    try {
      // Use the API endpoint for proper validation
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerData),
      })

      const result = await response.json()

      if (!response.ok) {
        console.error('API error:', result)
        throw new Error(result.message || 'Failed to create customer')
      }

      if (result.status === 'success' && result.data) {
        const newCustomer = result.data
        setCustomers(prev => [newCustomer, ...prev])
        return newCustomer
      }

      throw new Error('Invalid response from server')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create customer'
      setError(errorMessage)
      console.error('Create customer error:', err)
      return null
    }
  }

  const updateCustomer = async (id: string, customerData: Partial<Customer>): Promise<Customer | null> => {
    try {
      const updatedCustomer = await customersService.updateCustomer(id, customerData)
      if (updatedCustomer) {
        setCustomers(prev => prev.map(customer => 
          customer.id === id ? updatedCustomer : customer
        ))
      }
      return updatedCustomer
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update customer'
      setError(errorMessage)
      console.error('Update customer error:', err)
      return null
    }
  }

  const deleteCustomer = async (id: string): Promise<boolean> => {
    try {
      const success = await customersService.deleteCustomer(id)
      if (success) {
        setCustomers(prev => prev.filter(customer => customer.id !== id))
      }
      return success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete customer'
      setError(errorMessage)
      console.error('Delete customer error:', err)
      return false
    }
  }

  const getCustomerAppointmentCount = async (customerId: string): Promise<number> => {
    try {
      return await customersService.getCustomerAppointmentCount(customerId)
    } catch (err) {
      console.error('Get customer appointment count error:', err)
      return 0
    }
  }

  const getCustomerProjectCount = async (customerId: string): Promise<number> => {
    try {
      return await customersService.getCustomerProjectCount(customerId)
    } catch (err) {
      console.error('Get customer project count error:', err)
      return 0
    }
  }

  useEffect(() => {
    fetchCustomers()
  }, [])

  return {
    customers,
    loading,
    error,
    refetch: fetchCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomerAppointmentCount,
    getCustomerProjectCount,
  }
}
