import { NextResponse } from 'next/server'
import { appointmentsService } from '@/lib/services/appointments-service'

export async function GET() {
  try {
    const stats = await appointmentsService.getAppointmentStats()
    
    return NextResponse.json({
      status: 'success',
      data: stats
    })
    
  } catch (error) {
    console.error('Appointment stats API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch appointment statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 