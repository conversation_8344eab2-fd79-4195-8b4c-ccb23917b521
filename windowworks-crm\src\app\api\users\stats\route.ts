import { NextResponse } from 'next/server'
import { usersService } from '@/lib/services/users-service'

export async function GET() {
  try {
    const stats = await usersService.getUserStats()
    
    return NextResponse.json({
      status: 'success',
      data: stats
    })
    
  } catch (error) {
    console.error('User stats API error:', error)
    return NextResponse.json({ 
      status: 'error', 
      message: 'Failed to fetch user statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 